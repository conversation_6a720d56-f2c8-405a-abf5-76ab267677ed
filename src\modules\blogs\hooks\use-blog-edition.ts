import { useQueryClient } from "@tanstack/react-query";
import { FormEvent, useRef, useState } from "react";
import { CustomError } from "@/utils/custom-error";
import { useTranslations } from "next-intl";
import { useToast } from "@/hooks/use-toast";
import { MultilanguageSeoContentPayload } from "@/modules/seo/types/multilanguage-seo";
import { cleanBlogEditionFormData } from "../utils/form-data-cleaning/blog-edition-form";
import { validateBlogEditionData } from "../validation/blogs/validate-blog-creation-data";
import { updateBlogLanguageContent } from "../services/blog-services";

export default function useBlogEdition(
  getMetaContent: () => MultilanguageSeoContentPayload,
  getBlogId: () => string,
  onSuccessfulUpdate: () => void,
  onSlugChange?: (newSlug: string) => void
) {
  const t = useTranslations("warnings");
  const successT = useTranslations("shared.forms.upload");
  const blogsT = useTranslations("BlogsManagement");

  const queryClient = useQueryClient();

  const formRef = useRef<HTMLFormElement>(null);
  const [warning, setWarning] = useState("");
  const [isPending, setIsPending] = useState(false);
  const [activeLanguage, setActiveLanguage] = useState("french");
  const [formKey, setFormKey] = useState(0);

  const { toast } = useToast();

  const handleLanguageChange = (language: string) => {
    setFormKey((prev) => prev + 1);
    setActiveLanguage(language);
  };

  async function submitBlog(event: FormEvent) {
    if (!formRef.current) return;

    setIsPending(true);
    event.preventDefault();
    try {
      const formData = new FormData(formRef.current);

      const filteredFormData = cleanBlogEditionFormData(
        formData,
        activeLanguage
      );
      validateBlogEditionData(filteredFormData);

      const metaContentData = getMetaContent();
      filteredFormData.append(
        "metaContent",
        JSON.stringify(metaContentData.content)
      );

      setWarning("");

      const blogId = getBlogId();
      const response = await updateBlogLanguageContent(
        filteredFormData,
        blogId,
        activeLanguage
      );

      queryClient.invalidateQueries({
        queryKey: ["blogs"],
        exact: false,
      });

      if (response.data && response.data.slug && onSlugChange) {
        onSlugChange(response.data.slug);
      }

      toast({
        title: successT("successTitle"),
        description: successT("successDescription"),
      });

      onSuccessfulUpdate();

      // Use timeout to ensure state updates are processed
      setTimeout(() => {
        setFormKey((prev) => prev + 1);
      }, 0);
    } catch (error) {
      const customError = error as CustomError;
      if (customError.status === 500) {
        setWarning(t("serverError"));
        toast({ title: t("warning"), description: t("serverError") });
      } else if (customError.status === 400) {
        if (customError.message === "Missed Data!") {
          setWarning(t("upload.missedData"));
          toast({ title: t("warning"), description: t("upload.missedData") });
        } else {
          setWarning(t("upload.invalidFormat"));
          toast({
            title: t("warning"),
            description: t("upload.invalidFormat"),
          });
        }
      }
    } finally {
      setIsPending(false);
    }
  }

  return {
    submitBlog,
    formRef,
    warning,
    isPending,
    activeLanguage,
    handleLanguageChange,
    formKey,
  };
}
