import { useQueryClient } from "@tanstack/react-query";
import { FormEvent, useRef, useState } from "react";
import { CustomError } from "@/utils/custom-error";
import { useTranslations } from "next-intl";
import { useToast } from "@/hooks/use-toast";
import { MultilanguageSeoContentPayload } from "@/modules/seo/types/multilanguage-seo";
import { cleanCategoryEditionFormData } from "../../utils/form-data-cleaning/category-edition-form";
import { updateCategoryLanguageContent } from "../../services/categories/category-language-update";
import { validateCategoryEditionData } from "../../validation/categories/validate-category-edition-data";

export default function useCategoryEdition(
  getMetaContent: () => MultilanguageSeoContentPayload,
  getCategoryId: () => string,
  onSuccessfulUpdate: () => void,
  activeLanguage: string,
  setActiveLanguage: (language: string) => void,
  onSlugChange?: (newSlug: string) => void
) {
  const t = useTranslations("warnings");
  const successT = useTranslations("shared.forms.upload");
  const categoriesT = useTranslations("CategoriesManagement");

  const queryClient = useQueryClient();

  const formRef = useRef<HTMLFormElement>(null);
  const [warning, setWarning] = useState("");
  const [isPending, setIsPending] = useState(false);
  const [formKey, setFormKey] = useState(0);

  const { toast } = useToast();

  const handleLanguageChange = (language: string) => {
    setActiveLanguage(language);
    setFormKey((prev) => prev + 1);
  };

  async function submitCategory(event: FormEvent) {
    if (!formRef.current) return;

    setIsPending(true);
    event.preventDefault();
    try {
      const formData = new FormData(formRef.current);

      const filteredFormData = cleanCategoryEditionFormData(
        formData,
        activeLanguage
      );
      validateCategoryEditionData(filteredFormData);

      const metaContentData = getMetaContent();
      filteredFormData.append(
        "metaContent",
        JSON.stringify(metaContentData.content)
      );

      setWarning("");

      const categoryId = getCategoryId();
      const response = await updateCategoryLanguageContent(
        filteredFormData,
        categoryId,
        activeLanguage
      );

      queryClient.invalidateQueries({
        queryKey: ["categories"],
        exact: false,
      });

      toast({
        title: successT("successTitle"),
        description: successT("successDescription"),
      });

      if (response.data && response.data.slug && onSlugChange) {
        onSlugChange(response.data.slug);
      }

      onSuccessfulUpdate();
    } catch (error) {
      const customError = error as CustomError;
      if (customError.status === 500) {
        setWarning(t("serverError"));
        toast({ title: t("warning"), description: t("serverError") });
      } else if (customError.status === 400) {
        if (customError.message === "Missed Data!") {
          setWarning(t("upload.missedData"));
          toast({ title: t("warning"), description: t("upload.missedData") });
        } else {
          setWarning(t("upload.invalidFormat"));
          toast({
            title: t("warning"),
            description: t("upload.invalidFormat"),
          });
        }
      }
    } finally {
      setIsPending(false);
    }
  }

  return {
    submitCategory,
    formRef,
    warning,
    isPending,
    handleLanguageChange,
    formKey,
  };
}
