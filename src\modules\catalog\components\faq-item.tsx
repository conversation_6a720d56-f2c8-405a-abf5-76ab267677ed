"use client";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Card } from "@/components/ui/card";
import { Trash2 } from "lucide-react";
import { useTranslations } from "next-intl";
import Text from "@/styles/text-styles";
import WarnInput from "@/components/input/warn-input";
import { disableScrollOnNumberInput } from "@/utils/number-input";
import { Language } from "@/modules/seo/types/multilanguage-seo";

interface MultilanguageFaqContent {
  question: string;
  answer: string;
  language: string;
}

interface MultilanguageFaq {
  id?: string;
  displayOrder: number;
  content: MultilanguageFaqContent[];
}

interface FaqItemProps {
  faq: MultilanguageFaq;
  index: number;
  onUpdate: (faq: MultilanguageFaq) => void;
  onRemove: () => void;
  activeLanguage: Language;
}

export default function FaqItem({
  faq,
  index,
  onUpdate,
  onRemove,
  activeLanguage,
}: FaqItemProps) {
  const t = useTranslations("shared.forms.upload");

  const getCurrentLanguageContent = () => {
    const languageMap = {
      arabic: "Arabic",
      french: "French",
      english: "English",
    };

    if (!faq.content || !Array.isArray(faq.content)) {
      return {
        question: "",
        answer: "",
        language: languageMap[activeLanguage],
      };
    }

    return (
      faq.content.find(
        (content) => content.language === languageMap[activeLanguage]
      ) || { question: "", answer: "", language: languageMap[activeLanguage] }
    );
  };

  const handleContentChange = (field: "question" | "answer", value: string) => {
    const languageMap = {
      arabic: "Arabic",
      french: "French",
      english: "English",
    };

    const updatedContent = faq.content ? [...faq.content] : [];
    const currentLangContent = languageMap[activeLanguage];
    const existingIndex = updatedContent.findIndex(
      (content) => content.language === currentLangContent
    );

    if (existingIndex >= 0) {
      updatedContent[existingIndex] = {
        ...updatedContent[existingIndex],
        [field]: value,
      };
    } else {
      updatedContent.push({
        question: field === "question" ? value : "",
        answer: field === "answer" ? value : "",
        language: currentLangContent,
      });
    }

    onUpdate({
      ...faq,
      content: updatedContent,
    });
  };

  const handleDisplayOrderChange = (value: number) => {
    onUpdate({
      ...faq,
      displayOrder: value,
    });
  };

  return (
    <Card className="p-4 space-y-4">
      {faq.id && (
        <input type="hidden" name={`faqs[${index}].id`} value={faq.id} />
      )}
      <div className="flex justify-between items-center">
        <Text textStyle="TS6" className="font-medium">
          {t("productLabels.faq")} {index + 1}
        </Text>
        <Button
          type="button"
          variant="outline"
          size="icon"
          onClick={onRemove}
          className="text-red-500 hover:text-red-700"
        >
          <Trash2 className="w-4 h-4" />
        </Button>
      </div>

      {["arabic", "french", "english"].map((lang) => {
        const langKey = lang.charAt(0).toUpperCase() + lang.slice(1);
        const langContent = (faq.content || []).find(
          (content) => content.language === langKey
        ) || { question: "", answer: "", language: langKey };

        return (
          <div
            key={`${lang}-${faq.id || index}`}
            style={{ display: activeLanguage === lang ? "block" : "none" }}
            className="grid grid-cols-1 gap-4"
          >
            <div>
              <Label htmlFor={`faq-question-${index}-${lang}`}>
                {t("productLabels.faqQuestion")} {langKey} {t("required")}
              </Label>
              <Input
                key={`faq-question-${index}-${lang}-${faq.id || "new"}`}
                id={`faq-question-${index}-${lang}`}
                name={`faqs[${index}].question_${lang}`}
                defaultValue={langContent.question || ""}
                onChange={(e) =>
                  handleContentChange("question", e.target.value)
                }
                placeholder={t("productLabels.faqQuestion")}
              />
            </div>

            <div>
              <Label htmlFor={`faq-answer-${index}-${lang}`}>
                {t("productLabels.faqAnswer")} {langKey} {t("required")}
              </Label>
              <Textarea
                key={`faq-answer-${index}-${lang}-${faq.id || "new"}`}
                id={`faq-answer-${index}-${lang}`}
                name={`faqs[${index}].answer_${lang}`}
                defaultValue={langContent.answer || ""}
                onChange={(e) => handleContentChange("answer", e.target.value)}
                placeholder={t("productLabels.faqAnswer")}
                rows={3}
              />
            </div>
          </div>
        );
      })}

      <div>
        <Label htmlFor={`faq-displayOrder-${index}`}>
          {t("productLabels.displayOrder")} {t("optional")}
        </Label>
        <WarnInput
          id={`faq-displayOrder-${index}`}
          name={`faqs[${index}].displayOrder`}
          type="number"
          value={faq.displayOrder || ""}
          onChange={(e) =>
            handleDisplayOrderChange(parseInt(e.target.value) || 0)
          }
          onWheel={disableScrollOnNumberInput}
          placeholder={t("productLabels.displayOrder")}
        />
      </div>
    </Card>
  );
}
