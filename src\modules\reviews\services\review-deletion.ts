import { AxiosError } from "axios";
import { DELETE } from "@/lib/http-methods";
import { refreshToken } from "@/modules/auth/services/refresh-token";
import { CustomError } from "@/utils/custom-error";
import extractJWTokens from "@/modules/auth/utils/jwt/extract-tokens";

interface Response {
  ok: boolean;
  status: number;
  error: string;
}

export async function deleteReviewOnServerSide(
  reviewId: string
): Promise<Response> {
  const { access } = extractJWTokens();
  const headers = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const endpoint = `/reviews/${reviewId}`;

    await DELETE(`${process.env.BACKEND_ADDRESS}${endpoint}`, headers);

    return { ok: true, status: 200, error: "" };
  } catch (error) {
    const axiosError = error as AxiosError;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() => deleteReviewOnServerSide(reviewId));

      //unauthorized user error is already handled by the user hook
      if (!res) throw new CustomError("Unauthorized!", 401);

      return res;
    }

    if (axiosError.response?.status === 404) {
      throw new CustomError("This review is not found!", 404);
    }

    throw new CustomError("Server Error!", 500);
  }
}
