export default function cleanStoryFormData(
  formData: FormData,
  productId: string,
  elementId?: string
): FormData {
  const filteredFormData = new FormData();
  let imageDeleted = false;

  filteredFormData.append("productId", productId);

  formData.forEach((value, key) => {
    if (key === "deleted-image" && value === "null") {
      imageDeleted = true;
      return;
    }

    if (key === "defaultImage") {
      return;
    }

    // Image addition
    if (key === "image" && typeof value === "object") {
      if (value.size !== 0) filteredFormData.append(key, value);
    } else if (!elementId && typeof value === "string" && value.trim() !== "") {
      // Story creation
      filteredFormData.append(key, value.trim());
    } else if (elementId) {
      // Story edition
      if (typeof value === "string") {
        filteredFormData.append(key, value.trim());
      }
    }
  });

  if (imageDeleted && !filteredFormData.get("image")) {
    filteredFormData.append("image", "null");
  }

  return filteredFormData;
}
