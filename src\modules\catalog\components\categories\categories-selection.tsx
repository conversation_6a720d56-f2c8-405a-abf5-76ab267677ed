import { useTranslations } from "next-intl";
import { HTMLAttributes, useEffect, useState } from "react";
import useCategories from "../../hooks/categories/use-categories";
import { CategorySelectionType } from "../../types/categories";
import { cn } from "@/lib/utils";
import ElementsSelection from "@/components/elements-selection";
import { Label } from "@/components/ui/label";

interface Props extends HTMLAttributes<"div"> {
  setSelectedCategories: (categories: { id: string; slug: string }[]) => void;
  defaultSelectedCategoriesIds?: string[];
}

export default function CategoriesSelection({
  setSelectedCategories,
  defaultSelectedCategoriesIds,
  className,
}: Props) {
  const uploadContent = useTranslations("shared.forms.upload");

  const { categories, categoriesAreLoading } = useCategories();
  const [categoriesSelection, setCategoriesSelection] = useState<
    CategorySelectionType[]
  >([]);

  const selectedCategories = categoriesSelection.filter((cat) => cat.selected);
  const subCategoriesSelection = (
    selectedCategories.length > 0 ? selectedCategories : categoriesSelection
  ).flatMap((cat) => cat.subCategories);

  const selectedSubCategories = subCategoriesSelection.filter(
    (cat) => cat.selected
  );
  const subSubCategoriesSelection =
    selectedSubCategories.length > 0
      ? selectedSubCategories.flatMap((cat) => cat.subCategories)
      : (selectedCategories.length > 0
          ? selectedCategories
          : categoriesSelection
        )
          .flatMap((cat) => cat.subCategories)
          .flatMap((cat) => cat.subCategories);

  useEffect(() => {
    if (categories && categories.length > 0) {
      const categoriesSelection = categories.map(
        (category) =>
          ({
            ...category,
            subCategories: category.subCategories.map((subCat) => ({
              ...subCat,
              subCategories: subCat.subCategories.map((subSubCat) => ({
                ...subSubCat,
                subCategories: [],
                selected: defaultSelectedCategoriesIds
                  ? defaultSelectedCategoriesIds.includes(subSubCat.id)
                  : false,
              })),
              selected: defaultSelectedCategoriesIds
                ? defaultSelectedCategoriesIds.includes(subCat.id)
                : false,
            })),
            selected: defaultSelectedCategoriesIds
              ? defaultSelectedCategoriesIds.includes(category.id)
              : false,
          } as CategorySelectionType)
      );
      setCategoriesSelection(categoriesSelection);
    }
  }, [categories, defaultSelectedCategoriesIds]);
  const setCategories = (categoriesSlugs: string[]) => {
    categoriesSelection.forEach((categoryToSelect) => {
      if (categoriesSlugs.includes(categoryToSelect.slug))
        categoryToSelect.selected = true;
      else categoryToSelect.selected = false;
    });

    setCategoriesSelection([...categoriesSelection]);
  };

  const setSubCategories = (categoriesSlugs: string[]) => {
    subCategoriesSelection.forEach((categoryToSelect) => {
      if (categoriesSlugs.includes(categoryToSelect.slug))
        categoryToSelect.selected = true;
      else categoryToSelect.selected = false;
    });

    setCategoriesSelection([...categoriesSelection]);
  };

  const setSubSubCategoriesSelection = (categoriesSlugs: string[]) => {
    subSubCategoriesSelection.forEach((categoryToSelect) => {
      if (categoriesSlugs.includes(categoryToSelect.slug))
        categoryToSelect.selected = true;
      else categoryToSelect.selected = false;
    });

    setCategoriesSelection([...categoriesSelection]);
  };

  //updating the categories list once user select new categories
  useEffect(() => {
    const selectedCategories = categoriesSelection
      .filter((cat) => cat.selected)
      .map((cat) => ({
        id: cat.id,
        slug: cat.slug,
      }));

    const selectedSubCategories = subCategoriesSelection
      .filter((cat) => cat.selected)
      .map((cat) => ({
        id: cat.id,
        slug: cat.slug,
      }));

    const selectedSubSubCategories = subSubCategoriesSelection
      .filter((cat) => cat.selected)
      .map((cat) => ({
        id: cat.id,
        slug: cat.slug,
      }));

    setSelectedCategories([
      ...selectedCategories,
      ...selectedSubCategories,
      ...selectedSubSubCategories,
    ]);
  }, [categoriesSelection]);

  return (
    !categoriesAreLoading && (
      <div
        className={cn(
          "flex flex-col 2L:flex-row gap-5 L:gap-6 mt-6",
          className
        )}
      >
        {/* Category mere */}
        <div className="w-full flex flex-col space-y-3">
          <Label htmlFor="categoryId">
            {`${uploadContent("categoryLabels.category")}`}
          </Label>
          <ElementsSelection
            data={categories.map((category) => ({
              id: category.slug,
              name: category.name,
            }))}
            name="categoryId"
            values={categoriesSelection
              .filter((cat) => cat.selected)
              .map((cat) => cat.slug)}
            setValues={setCategories}
            selectAllButtonIsUsed
          />
        </div>
        {/* Commented out for now - can be used in the future */}
        {/* Sous Category mere */}
        {/* <div className="w-full flex flex-col space-y-3">
          <Label htmlFor="subCategoryId">
            {`${uploadContent("categoryLabels.categoryType")}`}
          </Label>
          <ElementsSelection
            data={subCategoriesSelection.map((cat) => ({
              id: cat.slug,
              name: cat.name,
            }))}
            name="subCategoryId"
            values={subCategoriesSelection
              .filter((cat) => cat.selected)
              .map((cat) => cat.slug)}
            setValues={setSubCategories}
            selectAllButtonIsUsed
          />
        </div> */}
        {/* Commented out for now - can be used in the future */}
        {/* Category  */}
        {/* <div className="w-full flex flex-col space-y-3">
          <Label htmlFor="subSubCategoryIds">
            {`${uploadContent("categoryLabels.category")}`}
          </Label>
          <ElementsSelection
            data={subSubCategoriesSelection.map((cat) => ({
              id: cat.slug,
              name: cat.name,
            }))}
            name="subSubCategoryIds"
            values={subSubCategoriesSelection
              .filter((cat) => cat.selected)
              .map((cat) => cat.slug)}
            setValues={setSubSubCategoriesSelection}
            selectAllButtonIsUsed
          />
        </div> */}
      </div>
    )
  );
}
