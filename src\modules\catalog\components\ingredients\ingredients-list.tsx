"use client";
import { useTranslations } from "next-intl";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { AlertDialog } from "@/components/ui/alert-dialog";
import { PlusIcon } from "lucide-react";
import Text from "@/styles/text-styles";
import ModalDialog from "@/components/modal-dialog";
import { IngredientType } from "@/modules/catalog/types/products";
import useIngredientUpload from "../../hooks/ingredients/use-ingredient-upload";
import useIngredientDelete from "../../hooks/ingredients/use-ingredient-delete";
import IngredientDialog from "./ingredient-dialog";
import IngredientCard from "./ingredient-card";

interface IngredientsListProps {
  ingredients: IngredientType[];
  productId: string;
  onIngredientsRefetch: () => void;
  className?: string;
}

export default function IngredientsList({
  ingredients,
  productId,
  onIngredientsRefetch,
  className = "",
}: IngredientsListProps) {
  const t = useTranslations("shared.forms.upload");
  const deleteTranslations = useTranslations("ProductsManagement.dialog");

  const {
    isCreateDialogOpen,
    editingIngredient,
    handleCreateIngredient,
    handleEditIngredient,
    handleCreateSuccess,
    handleEditSuccess,
    handleCloseCreateDialog,
    handleCloseEditDialog,
  } = useIngredientUpload({
    productId,
    onSuccess: onIngredientsRefetch,
  });

  const {
    deletingIngredient,
    isPending: isDeleting,
    handleDeleteIngredient,
    handleConfirmDelete,
    handleCancelDelete,
  } = useIngredientDelete({
    productId,
    onSuccess: onIngredientsRefetch,
  });

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex justify-between items-center">
        <Text textStyle="TS5" className="font-bold text-black">
          {t("productLabels.ingredients")}
        </Text>
        <Button
          type="button"
          onClick={handleCreateIngredient}
          className="bg-blue text-white hover:bg-blue/90 flex items-center gap-2"
        >
          <PlusIcon className="w-4 h-4" />
          {t("productLabels.add")}
        </Button>
      </div>

      <div className="space-y-3">
        {ingredients.map((ingredient, index) => (
          <IngredientCard
            key={ingredient.id || index}
            ingredient={ingredient}
            index={index}
            onEdit={handleEditIngredient}
            onDelete={handleDeleteIngredient}
          />
        ))}
      </div>

      {ingredients.length === 0 && (
        <Card className="p-6 text-center">
          <Text textStyle="TS6" className="text-gray-500">
            {t("productLabels.noItemsFound")}
          </Text>
        </Card>
      )}

      {/* Create Ingredient Dialog */}
      <IngredientDialog
        isOpen={isCreateDialogOpen}
        onClose={handleCloseCreateDialog}
        onSuccess={handleCreateSuccess}
        productId={productId}
        nextDisplayOrder={ingredients.length + 1}
      />

      {/* Edit Ingredient Dialog */}
      {editingIngredient && (
        <IngredientDialog
          isOpen={true}
          onClose={handleCloseEditDialog}
          onSuccess={handleEditSuccess}
          productId={productId}
          ingredient={editingIngredient}
          isEditing={true}
        />
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!deletingIngredient}>
        <ModalDialog
          title={deleteTranslations("title")}
          details={deleteTranslations("details")}
          cancel={deleteTranslations("cancel")}
          confirm={deleteTranslations("confirm")}
          onCancel={handleCancelDelete}
          onConfirm={handleConfirmDelete}
          isPending={isDeleting}
          theme="red"
        />
      </AlertDialog>
    </div>
  );
}
