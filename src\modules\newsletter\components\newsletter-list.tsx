"use client";
import { AlertDialog } from "@/components/ui/alert-dialog";
import ModalDialog from "@/components/modal-dialog";
import { useTranslations } from "next-intl";
import TrashIcon from "@assets/icons/management/trash-icon";
import Text from "@/styles/text-styles";
import { Skeleton } from "@/components/ui/skeleton";
import NoDataFound from "@/components/no-data-found";
import PaginationMangement from "@/components/pagination/pagination-management";
import DashboardListsContainer, {
  DashboardListsContainerSkeleton,
} from "@/components/dashbord-lists-container";
import Searchbar from "@/components/searchbar";
import useNewsletterEmails from "../hooks/use-newsletter-emails";
import useNewsletterDeletion from "../hooks/use-newsletter-deletion";
import NewsletterEmailContainer from "./newsletter-email-container";

export default function NewsletterList() {
  const {
    emails,
    emailsAreLoading,
    searchedEmails,
    handleSearch,
    page,
    setPage,
    pagesNumber,
    records,
  } = useNewsletterEmails({ limit: 20, paginationAffectUrl: true });

  const t = useTranslations("NewsletterManagement");

  const {
    isPending,
    emailsToDelete,
    addEmailToDeletionList,
    removeEmailFromDeletionList,
    deleteEmails,
    alertModalIsOpen,
    cancelDeletion,
    onDelete,
  } = useNewsletterDeletion();

  return (
    <DashboardListsContainer
      title={t("title")}
      headerElement={
        <Searchbar
          searchedContent={searchedEmails}
          setSearchedContent={handleSearch}
        />
      }
    >
      {!(emailsAreLoading || emails === undefined) ? (
        <div className="flex flex-col space-y-5 mt-3">
          {emails && emails.length > 0 && (
            <div className="w-full flex regularL:flex-row flex-col justify-end mt-3">
              <button
                onClick={onDelete}
                className="bg-red text-white self-end regularL:order-1 order-2 w-fit rounded-[20px] px-4 p-2 flex items-center regularL:gap-4 gap-2 active:opacity-80 active:scale-95 duration-300"
                id="delete-emails-button"
              >
                <TrashIcon />
                <Text textStyle="TS7">{t("delete")}</Text>
              </button>
            </div>
          )}
          <div className="flex flex-col space-y-2">
            {emails && emails.length > 0 ? (
              emails.map((email) => (
                <NewsletterEmailContainer
                  key={email.id}
                  email={email}
                  addEmailToDeletionList={addEmailToDeletionList}
                  removeEmailFromDeletionList={removeEmailFromDeletionList}
                />
              ))
            ) : (
              <NoDataFound className="py-5 w-full min-h-48 flex justify-center items-center" />
            )}
            <AlertDialog open={alertModalIsOpen}>
              <ModalDialog
                title={t("deleteConfirmTitle")}
                details={t("deleteConfirmDescription")}
                cancel={t("cancel")}
                confirm={t("confirm")}
                theme="red"
                isPending={isPending}
                onCancel={cancelDeletion}
                onConfirm={deleteEmails}
              />
            </AlertDialog>
          </div>
          {pagesNumber > 1 && (
            <div className="flex justify-center">
              <PaginationMangement
                records={records}
                currentPage={page}
                pagesNumber={pagesNumber}
                changePage={setPage}
              />
            </div>
          )}
        </div>
      ) : (
        <DashboardListsContainerSkeleton>
          <div className="w-full flex flex-col space-y-3">
            <div className="w-full flex justify-end space-x-2">
              <Skeleton className="h-8 L:w-48 w-32 rounded-[20px]" />
            </div>
            <div className="flex flex-col gap-2">
              {Array.from({ length: 5 }).map((_, idx) => (
                <Skeleton key={idx} className="h-16 w-full rounded-lg" />
              ))}
            </div>
          </div>
        </DashboardListsContainerSkeleton>
      )}
    </DashboardListsContainer>
  );
}
