import { useQueryClient } from "@tanstack/react-query";
import { FormEvent, useRef, useState } from "react";
import { CustomError } from "@/utils/custom-error";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import uploadProductToServerSide from "../../services/products/product-upload";
import { formDataJsonTransformation } from "../../utils/form-data-json-transformation";
import { MultilanguageSeoContentPayload } from "@/modules/seo/types/multilanguage-seo";
import { usePreviousUrl } from "@/hooks/urls-management/use-previous-url";
import cleanProductCreationFormData from "../../utils/form-data-cleaning/product-creation-form";
import { validateProductCreationData } from "../../validation/products/validate-product-creation-data";

export default function useProductCreation(
  getMetaContent: () => MultilanguageSeoContentPayload
) {
  const t = useTranslations("warnings");
  const productsT = useTranslations("ProductsManagement");

  const queryClient = useQueryClient();
  const router = useRouter();
  const previousUrl = usePreviousUrl();

  const formRef = useRef<HTMLFormElement>(null);
  const [warning, setWarning] = useState("");
  const [isPending, setIsPending] = useState(false);
  const [activeLanguage, setActiveLanguage] = useState("french");

  const { toast } = useToast();

  const handleLanguageChange = (language: string) => {
    setActiveLanguage(language);
  };

  async function submitProduct(event: FormEvent) {
    if (!formRef.current) return;

    setIsPending(true);
    event.preventDefault();
    try {
      const formData = new FormData(formRef.current);

      const filteredFormData = cleanProductCreationFormData(formData);
      validateProductCreationData(filteredFormData);

      const metaContentData = getMetaContent();
      filteredFormData.append(
        "metaContent",
        JSON.stringify(metaContentData.content)
      );

      setWarning("");

      const submittedData: Record<string, any> =
        formDataJsonTransformation(filteredFormData);

      // Parse JSON strings back to objects to avoid double encoding
      if (submittedData.content && typeof submittedData.content === "string") {
        submittedData.content = JSON.parse(submittedData.content);
      }
      if (
        submittedData.metaContent &&
        typeof submittedData.metaContent === "string"
      ) {
        submittedData.metaContent = JSON.parse(submittedData.metaContent);
      }
      if (submittedData.faqs && typeof submittedData.faqs === "string") {
        submittedData.faqs = JSON.parse(submittedData.faqs);
      }
      if (
        submittedData.sections &&
        typeof submittedData.sections === "string"
      ) {
        submittedData.sections = JSON.parse(submittedData.sections);
      }

      await uploadProductToServerSide(submittedData);

      queryClient.invalidateQueries({
        queryKey: ["products"],
        exact: false,
      });

      if (previousUrl && previousUrl.startsWith("/products"))
        router.push(previousUrl);
      else router.push("/products");
    } catch (error) {
      const customError = error as CustomError;
      if (customError.status === 500) {
        setWarning(t("serverError"));
        toast({ title: t("warning"), description: t("serverError") });
      } else if (customError.status === 400) {
        if (customError.message === "Missed Data!") {
          setWarning(t("upload.missedData"));
          toast({ title: t("warning"), description: t("upload.missedData") });
        } else if (customError.code === "P6000") {
          setWarning(t("upload.duplicateProduct"));
          toast({
            title: t("warning"),
            description: t("upload.duplicateProduct"),
          });
        } else {
          setWarning(t("upload.invalidFormat"));
          toast({
            title: t("warning"),
            description: t("upload.invalidFormat"),
          });
        }
      } else if (customError.status === 404) {
        setWarning(t("upload.categoryNotFound"));
        toast({
          title: t("warning"),
          description: t("upload.categoryNotFound"),
        });
      }
    } finally {
      setIsPending(false);
    }
  }

  return {
    submitProduct,
    formRef,
    warning,
    isPending,
    activeLanguage,
    handleLanguageChange,
  };
}
