export interface ReviewType {
  id: string;
  rating: number;
  user: {
    id: string;
    email: string;
    name: string;
  };
  comment: string;
  createdAt: string;
  updatedAt: string;
}

export interface ReviewInResponseType {
  id: string;
  rating: number;
  user: {
    id: string;
    email: string;
    name: string;
  };
  comment: string;
  createdAt: string;
  updatedAt: string;
}

export interface ReviewsResponseType {
  pagination: {
    records: number;
    currentPage: number;
    totalPages: number;
  };
  data: ReviewInResponseType[];
}

export function castToReviewType(review: ReviewInResponseType): ReviewType {
  return {
    id: review.id,
    rating: review.rating,
    user: {
      id: review.user.id,
      email: review.user.email,
      name: review.user.name,
    },
    comment: review.comment,
    createdAt: review.createdAt,
    updatedAt: review.updatedAt,
  };
}

export function castToReviewsResponseType(response: ReviewsResponseType): {
  reviews: ReviewType[];
  pagination: {
    records: number;
    currentPage: number;
    totalPages: number;
  };
} {
  return {
    reviews: response.data.map(castToReviewType),
    pagination: response.pagination,
  };
}
