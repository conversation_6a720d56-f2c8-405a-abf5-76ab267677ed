import { useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import useUser from "@/modules/auth/hooks/use-user";
import { CustomError } from "@/utils/custom-error";
import { deleteNewsletterEmailOnServerSide } from "../services/newsletter-deletion";
import { useTranslations } from "next-intl";

export default function useNewsletterDeletion() {
  const queryClient = useQueryClient();
  const [alertModalIsOpen, setAlertModalIsOpen] = useState(false);
  const [warning, setWarning] = useState("");
  const [emailsToDelete, setEmailsToDelete] = useState<string[]>([]);
  const { user } = useUser();
  const t = useTranslations("warnings");
  const [isPending, setIsPending] = useState(false);
  
  const deleteEmails = async () => {
    setIsPending(true);

    try {
      await Promise.all(
        emailsToDelete.map((emailId) => deleteNewsletterEmailOnServerSide({ emailId }))
      );

      if (warning !== "") setWarning("");

      queryClient.invalidateQueries({
        queryKey: ["newsletter-emails"],
        exact: false,
      });

      setAlertModalIsOpen(false);
      setEmailsToDelete([]);
    } catch (throwedError) {
      const error = throwedError as CustomError;
      if (error.status === 500) setWarning(t("serverError"));
    }

    setIsPending(false);
  };

  const onDelete = () => {
    if (emailsToDelete.length > 0) setAlertModalIsOpen(true);
  };

  const addEmailToDeletionList = (emailId: string) => {
    setEmailsToDelete([...emailsToDelete, emailId]);
  };

  const removeEmailFromDeletionList = (emailId: string) => {
    const filterList = emailsToDelete.filter((id) => emailId !== id);
    setEmailsToDelete(filterList);
  };

  const requireDirectDeletion = (emailId: string) => {
    setEmailsToDelete([emailId]);
    setAlertModalIsOpen(true);
  };

  const cancelDeletion = () => {
    setAlertModalIsOpen(false);
  };

  return {
    isPending,
    alertModalIsOpen,
    cancelDeletion,
    warning,
    deleteEmails,
    setEmailsToDelete,
    addEmailToDeletionList,
    removeEmailFromDeletionList,
    onDelete,
    emailsToDelete,
    requireDirectDeletion,
  };
}
