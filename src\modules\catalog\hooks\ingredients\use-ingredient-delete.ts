import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import { useTranslations } from "next-intl";
import { useToast } from "@/hooks/use-toast";
import { CustomError } from "@/utils/custom-error";
import { IngredientType } from "@/modules/catalog/types/products";
import deleteIngredientOnServerSide from "../../services/ingredients/ingredient-delete";

interface UseIngredientDeleteProps {
  productId: string;
  onSuccess?: () => void;
}

export default function useIngredientDelete({
  productId,
  onSuccess,
}: UseIngredientDeleteProps) {
  const queryClient = useQueryClient();
  const t = useTranslations("warnings");
  const { toast } = useToast();
  const [deletingIngredient, setDeletingIngredient] = useState<IngredientType | null>(null);

  const { mutate: deleteIngredient, isPending } = useMutation<
    void,
    CustomError,
    IngredientType
  >({
    mutationFn: (ingredient: IngredientType) => deleteIngredientOnServerSide(ingredient),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["product", productId],
        exact: false,
      });

      toast({
        title: "Succès",
        description: "Ingrédient supprimé avec succès",
      });

      setDeletingIngredient(null);
      onSuccess?.();
    },
    onError: (error) => {
      let errorMessage = t("serverError");

      if (error.status === 400) {
        errorMessage = t("upload.missedData");
      } else if (error.status === 404) {
        errorMessage = "Ingrédient ou produit non trouvé";
      }

      toast({
        title: "Erreur",
        description: errorMessage,
        variant: "destructive",
      });
    },
  });

  const handleDeleteIngredient = (ingredient: IngredientType) => {
    setDeletingIngredient(ingredient);
  };

  const handleConfirmDelete = () => {
    if (deletingIngredient) {
      deleteIngredient(deletingIngredient);
    }
  };

  const handleCancelDelete = () => {
    setDeletingIngredient(null);
  };

  return {
    deletingIngredient,
    isPending,
    handleDeleteIngredient,
    handleConfirmDelete,
    handleCancelDelete,
  };
}
