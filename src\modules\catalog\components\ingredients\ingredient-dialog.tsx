"use client";
import { useTranslations } from "next-intl";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Title,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import Text from "@/styles/text-styles";
import { disableScrollOnNumberInput } from "@/utils/number-input";
import { IngredientType } from "@/modules/catalog/types/products";
import ImageUpload from "../images-management/image-upload";
import useIngredientUpload from "../../hooks/ingredients/use-ingredient-upload";

interface IngredientDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  productId: string;
  ingredient?: IngredientType;
  isEditing?: boolean;
  nextDisplayOrder?: number;
}

export default function IngredientDialog({
  isOpen,
  onClose,
  onSuccess,
  productId,
  ingredient,
  isEditing = false,
  nextDisplayOrder = 1,
}: IngredientDialogProps) {
  const t = useTranslations("shared.forms.upload");

  const { isPending, submitIngredient, warning, formRef } = useIngredientUpload(
    {
      productId,
      ingredient,
      isEditing,
      onSuccess: () => {
        onSuccess();
        handleClose();
      },
    }
  );

  const handleClose = () => {
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? "Modifier l'ingrédient" : "Ajouter un ingrédient"}
          </DialogTitle>
        </DialogHeader>

        <form ref={formRef} onSubmit={submitIngredient} className="space-y-4">
          {warning && (
            <Text textStyle="TS7" className="text-red mt-1">
              {warning}
            </Text>
          )}
          <div>
            <Label htmlFor="ingredient-title">
              {t("productLabels.ingredientTitle")} {t("required")}
            </Label>
            <Input
              id="title"
              name="title"
              defaultValue={isEditing && ingredient ? ingredient.title : ""}
              placeholder={t("productLabels.ingredientTitle")}
            />
          </div>

          <div>
            <Label htmlFor="ingredient-description">
              {t("productLabels.ingredientDescription")} {t("optional")}
            </Label>
            <Textarea
              id="description"
              name="description"
              defaultValue={
                isEditing && ingredient ? ingredient.description : ""
              }
              placeholder={t("productLabels.ingredientDescription")}
              rows={4}
            />
          </div>

          <div>
            <Label htmlFor="image">
              {t("productLabels.image")} {t("required")}
            </Label>
            <ImageUpload
              name="image"
              defaultSrc={isEditing && ingredient ? ingredient.image : null}
            />
          </div>

          <div>
            <Label htmlFor="displayOrder">
              {t("productLabels.displayOrder")} {t("optional")}
            </Label>
            <Input
              id="displayOrder"
              name="displayOrder"
              type="number"
              defaultValue={
                isEditing && ingredient
                  ? ingredient.displayOrder?.toString() || "1"
                  : nextDisplayOrder.toString()
              }
              onWheel={disableScrollOnNumberInput}
              placeholder={t("productLabels.displayOrder")}
            />
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isPending}
            >
              {t("cancel")}
            </Button>
            <Button
              type="submit"
              disabled={isPending}
              className="bg-blue text-white hover:bg-blue/90"
            >
              {isPending ? "Enregistrement..." : t("save")}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
