import {
  IngredientInResponseType,
  IngredientType,
} from "@/modules/catalog/types/products";

export function castToIngredientType(
  ingredient: IngredientInResponseType
): IngredientType {
  return {
    title: ingredient.title,
    id: ingredient.id,
    description: ingredient.description,
    displayOrder: ingredient.displayOrder,
    image: ingredient.image
      ? `${process.env.BACKEND_ADDRESS}${ingredient.image}`
      : undefined,
  };
}
