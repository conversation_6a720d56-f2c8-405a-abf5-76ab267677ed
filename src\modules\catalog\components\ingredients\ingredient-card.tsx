"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Edit, Trash2 } from "lucide-react";
import Text from "@/styles/text-styles";
import { IngredientType } from "@/modules/catalog/types/products";

interface IngredientCardProps {
  ingredient: IngredientType;
  index?: number;
  onEdit: (ingredient: IngredientType) => void;
  onDelete: (ingredient: IngredientType) => void;
  className?: string;
}

export default function IngredientCard({
  ingredient,
  index,
  onEdit,
  onDelete,
  className = "",
}: IngredientCardProps) {
  return (
    <Card key={ingredient.id || index} className={`p-4 ${className}`}>
      <div className="flex justify-between items-start">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-2">
            <Text textStyle="TS6" className="font-bold">
              {ingredient.title}
            </Text>
          </div>
          <Text textStyle="TS7" className="text-gray-600 mb-2">
            {ingredient.description}
          </Text>
          {ingredient.image && (
            <div className="mt-2">
              <img
                src={ingredient.image}
                alt={ingredient.title}
                className="w-20 h-20 object-cover rounded"
              />
            </div>
          )}
        </div>
        <div className="flex gap-2 ml-4">
          <Button
            type="button"
            variant="outline"
            size="icon"
            onClick={() => onEdit(ingredient)}
            className="text-blue-500 hover:text-blue-700"
          >
            <Edit className="w-4 h-4" />
          </Button>
          <Button
            type="button"
            variant="outline"
            size="icon"
            onClick={() => onDelete(ingredient)}
            className="text-red-500 hover:text-red-700"
          >
            <Trash2 className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </Card>
  );
}
