import { AxiosError } from "axios";
import { GET } from "@/lib/http-methods";
import { refreshToken } from "@/modules/auth/services/refresh-token";
import {
  ReviewsResponseType,
  castToReviewsResponseType,
} from "../types/reviews";
import extractJWTokens from "@/modules/auth/utils/jwt/extract-tokens";

export async function retrieveReviews(
  productSlug: string,
  page: number = 1,
  limit: number = 5
) {
  const { access } = extractJWTokens();
  const headers = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const params = {
      params: {
        limit,
        page,
      },
    };

    const res = await GET(
      `/reviews/products/${productSlug}/dashboard`,
      headers,
      params
    );

    return castToReviewsResponseType(res.data as ReviewsResponseType);
  } catch (error) {
    const axiosError = error as AxiosError;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() =>
        retrieveReviews(productSlug, page, limit)
      );

      //unauthorized user error is already handled by the user hook
      if (!res) return null;

      return res;
    }

    return null;
  }
}
