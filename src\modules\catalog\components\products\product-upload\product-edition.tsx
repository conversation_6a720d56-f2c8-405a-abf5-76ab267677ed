"use client";
import WarnInput from "@/components/input/warn-input";
import Text from "@/styles/text-styles";
import FormSubmission from "../../form-submission";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import ItemsList from "../items-list";
import { Skeleton } from "@/components/ui/skeleton";
import { DashboardListsContainerSkeleton } from "@/components/dashbord-lists-container";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import useEditableProduct from "@/modules/catalog/hooks/products/use-editable-product";
import DynamicArrayManager from "../../dynamic-array-manager";
import SectionItem from "../../section-item";
import FaqItem from "../../faq-item";
import { useEffect, useState } from "react";
// import useBrands from "@/modules/catalog/hooks/brands/use-brands";
import { Switch } from "@/components/ui/switch";
import useSeoMetaData from "@/modules/seo/hooks/use-seo-meta-data";
import { usePreviousUrl } from "@/hooks/urls-management/use-previous-url";
import { disableScrollOnNumberInput } from "@/utils/number-input";
import StoriesList from "../../stories/stories-list";
import IngredientsList from "../../ingredients/ingredients-list";
import useCategories from "@/modules/catalog/hooks/categories/use-categories";
import CategoriesSelection from "../../categories/categories-selection";
import LanguageTabs from "../../brands/brand-upload/language-tabs";
import MultilanguageProductFormFields from "./multilanguage-product-form-fields";
import MultilanguageSeoContent from "@/modules/seo/components/seo-meta-content/multilanguage-seo-content";
import { Language } from "@/modules/seo/types/multilanguage-seo";
import useProductEdition from "@/modules/catalog/hooks/products/use-product-edition";

interface Props {
  productSlug: string;
}

export default function ProductEdition({ productSlug }: Props) {
  const uploadContent = useTranslations("shared.forms.upload");
  const t = useTranslations("ProductsManagement");
  const reviewsT = useTranslations("ReviewsManagement");
  const router = useRouter();
  const previousUrl = usePreviousUrl();

  // Commented out for now - can be used in the future
  // const [selectedBrand, setSelectedBrand] = useState<string | undefined>(
  //   undefined
  // );
  const [productVariationsIsDisplayed, setProductVariationsIsDisplayed] =
    useState(true);

  // State for sections and faqs
  const [sections, setSections] = useState<any[]>([]);
  const [faqs, setFaqs] = useState<any[]>([]);
  const [originalFaqs, setOriginalFaqs] = useState<any[]>([]);
  const [originalSections, setOriginalSections] = useState<any[]>([]);

  const { categories, categoriesAreLoading } = useCategories();
  // const { brands, brandsAreLoading } = useBrands({});

  const [selectedCategoriesIds, setSelectedCategoriesIds] = useState<
    { id: string; slug: string }[]
  >([]);

  // Initialize activeLanguage state for hooks that need it
  const [activeLanguage, setActiveLanguage] = useState("french");

  const {
    metaContent,
    handleMetaTitleChange,
    handleMetaDescriptionChange,
    addNewKeyword,
    removeKeyword,
    getMultilanguageMetaContent,
  } = useSeoMetaData(activeLanguage as Language);

  const {
    product,
    isLoading: productIsLoading,
    refetch,
  } = useEditableProduct({
    productSlug: productSlug || "",
    language: activeLanguage,
  });

  const { formRef, submitProduct, warning, isPending, formKey } =
    useProductEdition(
      () => getMultilanguageMetaContent(),
      () => product?.id || "",
      () => {
        refetch();
      },
      (newSlug: string) => {
        const currentPath = window.location.pathname;
        const newPath = currentPath.replace(
          /\/products\/[^\/]+\/edit/,
          `/products/${newSlug}/edit`
        );
        router.replace(newPath);
      },
      () => originalFaqs,
      () => originalSections
    );

  const handleLanguageChange = (language: string) => {
    setActiveLanguage(language);
  };

  useEffect(() => {
    if (product) {
      // setSelectedBrand(product?.brand?.id);

      // Transform sections to multilingual format if needed
      const transformedSections = (product?.sections || []).map(
        (section: any) => {
          if (section.content && Array.isArray(section.content)) {
            return section; // Already in multilingual format
          } else {
            // Transform from old format to multilingual format - create content for all languages
            return {
              id: section.id,
              displayOrder: section.displayOrder || 1,
              content: [
                {
                  title: section.title || "",
                  description: section.description || "",
                  language: "Arabic",
                },
                {
                  title: section.title || "",
                  description: section.description || "",
                  language: "French",
                },
                {
                  title: section.title || "",
                  description: section.description || "",
                  language: "English",
                },
              ],
            };
          }
        }
      );

      // Transform FAQs to multilingual format if needed
      const transformedFaqs = (product?.faqs || []).map((faq: any) => {
        if (faq.content && Array.isArray(faq.content)) {
          return faq; // Already in multilingual format
        } else {
          // Transform from old format to multilingual format - create content for all languages
          return {
            id: faq.id,
            displayOrder: faq.displayOrder || 1,
            content: [
              {
                question: faq.question || "",
                answer: faq.answer || "",
                language: "Arabic",
              },
              {
                question: faq.question || "",
                answer: faq.answer || "",
                language: "French",
              },
              {
                question: faq.question || "",
                answer: faq.answer || "",
                language: "English",
              },
            ],
          };
        }
      });

      setSections(transformedSections);
      setFaqs(transformedFaqs);
      setOriginalSections(transformedSections);
      setOriginalFaqs(transformedFaqs);
    }
  }, [product]);

  const onCancelSubmission = () => {
    if (previousUrl && previousUrl.startsWith("/products")) {
      router.push(previousUrl);
    } else router.push("/products");
  };

  // return !(productIsLoading || product === undefined) &&
  //   !(brandsAreLoading || brands === undefined) &&
  return !(categoriesAreLoading || categories === undefined) &&
    !(productIsLoading || product === undefined) ? (
    <div className="flex flex-col py-3">
      <FormSubmission
        cancel={uploadContent("cancel")}
        submit={uploadContent("save")}
        isPending={isPending}
        onCancel={onCancelSubmission}
        onSubmit={submitProduct}
      >
        <div className="p-3 bg-white flex flex-col space-y-6">
          <Text textStyle="TS4" className="font-bold text-black">
            {t("productInfo")}
          </Text>

          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <Text textStyle="TS6" className="text-yellow-800">
              {uploadContent("saveBeforeSwitchingNote")}
            </Text>
          </div>

          <form
            ref={formRef}
            key={formKey}
            className="flex flex-col extraXL:flex-row gap-4"
          >
            <div className="basis-[70%] regularL:order-1 order-2 flex flex-col gap-5">
              {/* Language Content Card */}
              <div className="rounded-2xl border border-lightGray p-7 bg-white space-y-7">
                <LanguageTabs
                  options={[
                    { key: "arabic", value: t("languages.arabic") },
                    { key: "french", value: t("languages.french") },
                    { key: "english", value: t("languages.english") },
                  ]}
                  onSelect={handleLanguageChange}
                  selectedValue={activeLanguage}
                />
                <Text textStyle="TS4" className="font-bold text-black">
                  {t("productContent")}
                </Text>
                <div className="text-red self-center">{warning}</div>

                <div className="w-full">
                  <div
                    style={{
                      display: activeLanguage === "arabic" ? "block" : "none",
                    }}
                  >
                    <MultilanguageProductFormFields
                      multilanguage={true}
                      language="arabic"
                      initialName={product?.name || ""}
                      initialDescription={product?.description || ""}
                      initialDetails={product?.details || ""}
                    />
                  </div>

                  <div
                    style={{
                      display: activeLanguage === "french" ? "block" : "none",
                    }}
                  >
                    <MultilanguageProductFormFields
                      multilanguage={true}
                      language="french"
                      initialName={product?.name || ""}
                      initialDescription={product?.description || ""}
                      initialDetails={product?.details || ""}
                    />
                  </div>

                  <div
                    style={{
                      display: activeLanguage === "english" ? "block" : "none",
                    }}
                  >
                    <MultilanguageProductFormFields
                      multilanguage={true}
                      language="english"
                      initialName={product?.name || ""}
                      initialDescription={product?.description || ""}
                      initialDetails={product?.details || ""}
                    />
                  </div>
                </div>
              </div>

              {/* Product Settings Card */}
              <div className=" rounded-2xl border border-lightGray p-7 bg-white  space-y-7">
                <Text textStyle="TS5" className="font-bold text-black">
                  {t("productSettings")}
                </Text>

                {/* Commented out for now - can be used in the future */}
                {/* Brand */}
                {/* <div>
                  <div className="w-full flex flex-col space-y-3">
                    <Label htmlFor="brandId">
                      {`${uploadContent(
                        "brandLabels.brandType"
                      )} ${uploadContent("optional")}`}
                    </Label>
                    <ListPicker
                      data={brands}
                      onChange={(id) => setSelectedBrand(id)}
                      selectedElementId={selectedBrand || ""}
                      className="w-full max-w-full"
                      dropdownClassName="L:w-[300px] shadow-md border"
                      pickedElementName={"brandId"}
                    />
                  </div>
                </div> */}

                <div className="w-full flex flex-col space-y-3">
                  <Label htmlFor="displayOrder">
                    {uploadContent("productLabels.displayOrder")}{" "}
                    {uploadContent("optional")}
                  </Label>
                  <WarnInput
                    name="displayOrder"
                    type="number"
                    onWheel={disableScrollOnNumberInput}
                    warning=""
                    placeholder={uploadContent("productLabels.displayOrder")}
                    value={
                      product.displayOrder === 0
                        ? "0"
                        : product.displayOrder || ""
                    }
                  />
                </div>

                <CategoriesSelection
                  setSelectedCategories={setSelectedCategoriesIds}
                  defaultSelectedCategoriesIds={product.categoryIds}
                  className="flex flex-col 2L:flex-col gap-5 L:gap-6 mt-6"
                />
                {selectedCategoriesIds.length > 0 ? (
                  selectedCategoriesIds.map((cat) => (
                    <input
                      key={cat.id}
                      name={"categoryIds"}
                      className="hidden"
                      value={cat.id}
                      readOnly
                    />
                  ))
                ) : (
                  <input
                    name={"categoryIds"}
                    className="hidden"
                    value={""}
                    readOnly
                  />
                )}

                {/* Sections */}
                <DynamicArrayManager
                  title={uploadContent("productLabels.sections")}
                  items={sections}
                  onItemsChange={setSections}
                  renderItem={(section, index, onUpdate, onRemove) => (
                    <SectionItem
                      key={index}
                      section={section}
                      index={index}
                      onUpdate={onUpdate}
                      onRemove={onRemove}
                      activeLanguage={activeLanguage as Language}
                    />
                  )}
                  createNewItem={() => ({
                    displayOrder: sections.length + 1,
                    content: [],
                  })}
                  className="mt-6"
                />

                {/* FAQs */}
                <DynamicArrayManager
                  title={uploadContent("productLabels.faqs")}
                  items={faqs}
                  onItemsChange={setFaqs}
                  renderItem={(faq, index, onUpdate, onRemove) => (
                    <FaqItem
                      key={index}
                      faq={faq}
                      index={index}
                      onUpdate={onUpdate}
                      onRemove={onRemove}
                      activeLanguage={activeLanguage as Language}
                    />
                  )}
                  createNewItem={() => ({
                    displayOrder: faqs.length + 1,
                    content: [],
                  })}
                  className="mt-6"
                />

                {/* Stories */}
                <StoriesList
                  stories={product?.stories || []}
                  productId={product?.id || ""}
                  onStoriesRefetch={refetch}
                  className="mt-6"
                />

                {/* Ingredients */}
                <IngredientsList
                  ingredients={product?.ingredients || []}
                  productId={product?.id || ""}
                  onIngredientsRefetch={refetch}
                  className="mt-6"
                />
              </div>

              <div className=" rounded-2xl border border-lightGray p-7 bg-white  space-y-7">
                <Text textStyle="TS5" className="font-bold text-black">
                  {t("variations.title")}
                </Text>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="productVariationsIsDisplayed"
                    checked={productVariationsIsDisplayed}
                    onCheckedChange={setProductVariationsIsDisplayed}
                  />
                  <Label htmlFor="productVariationsIsDisplayed">
                    {t("variations.isDisplayVariation")}
                  </Label>
                </div>
                {productVariationsIsDisplayed && (
                  <>
                    <ItemsList
                      items={product.items}
                      product={{ id: product.id, slug: product.slug }}
                    />
                  </>
                )}
              </div>
            </div>
            <div className="basis-[30%] order-2 space-y-4">
              <div className="rounded-2xl border border-lightGray p-7 bg-white space-y-5">
                <MultilanguageSeoContent
                  metaContent={metaContent}
                  activeLanguage={activeLanguage as Language}
                  changeMetaTitle={handleMetaTitleChange}
                  changeMetaDescription={handleMetaDescriptionChange}
                  addNewKeyword={addNewKeyword}
                  removeKeyword={removeKeyword}
                />
              </div>
              <div className=" rounded-2xl border border-lightGray p-7 bg-white space-y-5">
                <Text textStyle="TS5" className="font-bold text-black">
                  {reviewsT("title")}
                </Text>
                <Button
                  type="button"
                  onClick={() =>
                    router.push(`/products/${productSlug}/reviews`)
                  }
                  className="w-full bg-blue text-white hover:bg-blue-600"
                >
                  {reviewsT("manageReviews")}
                </Button>
              </div>
            </div>
          </form>
        </div>
      </FormSubmission>
    </div>
  ) : (
    <div className="py-2 w-full flex XL:flex-row flex-col XL:gap-8 gap-4">
      <DashboardListsContainerSkeleton className="flex-1">
        {/* Product Information Section */}
        <div className="w-full flex flex-col space-y-4">
          <div className="w-full flex flex-col space-y-1">
            <Skeleton className="h-6 w-32" />
            <Skeleton className="h-9 w-full max-w-[500px]" />
          </div>
          <div className="w-full flex flex-col space-y-1">
            <Skeleton className="h-6 w-32" />
            <Skeleton className="h-9 w-full max-w-[500px]" />
          </div>
          <div className="w-full flex flex-col space-y-1">
            <Skeleton className="h-6 w-32" />
            <div className="flex gap-4">
              <Skeleton className="h-9 w-1/2" />
              <Skeleton className="h-9 w-1/2" />
            </div>
          </div>
          <div className="w-full flex flex-col space-y-1">
            <Skeleton className="h-6 w-32" />
            <Skeleton className="h-24 w-full max-w-[800px]" />
          </div>
          <div className="w-full flex flex-col space-y-1">
            <Skeleton className="h-6 w-32" />
            <Skeleton className="h-9 w-full max-w-[500px]" />
          </div>
          <div className="w-full flex flex-col space-y-1">
            <Skeleton className="h-6 w-32" />
            <div className="flex items-center gap-4">
              <Skeleton className="h-10 w-20" />
              <Skeleton className="h-10 w-24" />
              <Skeleton className="h-10 w-20" />
            </div>
            <div className="grid grid-cols-2 gap-4 mt-4">
              <div className="flex flex-col items-center">
                <Skeleton className="h-32 w-full" />
                <Skeleton className="h-6 w-32 mt-2" />
              </div>
              <div className="flex flex-col items-center">
                <Skeleton className="h-32 w-full" />
                <Skeleton className="h-6 w-32 mt-2" />
              </div>
              <div className="flex flex-col items-center">
                <Skeleton className="h-32 w-full" />
                <Skeleton className="h-6 w-32 mt-2" />
              </div>
              <div className="flex flex-col items-center">
                <Skeleton className="h-32 w-full" />
                <Skeleton className="h-6 w-32 mt-2" />
              </div>
            </div>
          </div>
        </div>
      </DashboardListsContainerSkeleton>

      <DashboardListsContainerSkeleton>
        {/* keywords & SEO Settings Section */}
        <div className="w-full flex flex-col space-y-4">
          <div className="w-full flex flex-col space-y-1">
            <Skeleton className="h-6 w-32" />
            <Skeleton className="h-9 w-full" />
            <div className="flex gap-2 mt-2">
              <Skeleton className="h-8 w-24" />
              <Skeleton className="h-8 w-24" />
              <Skeleton className="h-8 w-32" />
            </div>
          </div>
          <div className="w-full flex flex-col space-y-1">
            <Skeleton className="h-6 w-32" />
            <div className="w-full flex flex-col space-y-1">
              <Skeleton className="h-6 w-24" />
              <Skeleton className="h-9 w-full" />
            </div>
            <div className="w-full flex flex-col space-y-1">
              <Skeleton className="h-6 w-32" />
              <Skeleton className="h-9 w-full" />
            </div>
          </div>
        </div>
      </DashboardListsContainerSkeleton>
    </div>
  );
}
