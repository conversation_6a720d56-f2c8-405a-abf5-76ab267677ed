"use client";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Card } from "@/components/ui/card";
import { Trash2 } from "lucide-react";
import { useTranslations } from "next-intl";
import Text from "@/styles/text-styles";
import WarnInput from "@/components/input/warn-input";
import { disableScrollOnNumberInput } from "@/utils/number-input";
import { StoryType } from "@/modules/catalog/types/products";
import ImageUpload from "../images-management/image-upload";

interface StoryItemProps {
  story: StoryType;
  index: number;
  onUpdate: (story: StoryType) => void;
  onRemove: () => void;
}

export default function StoryItem({
  story,
  index,
  onUpdate,
  onRemove,
}: StoryItemProps) {
  const t = useTranslations("shared.forms.upload");

  const handleChange = (field: string, value: string | number) => {
    onUpdate({ ...story, [field]: value });
  };

  return (
    <Card className="p-4 space-y-4">
      <div className="flex justify-between items-center">
        <Text textStyle="TS6" className="font-medium">
          {t("productLabels.story")} {index + 1}
        </Text>
        <Button
          type="button"
          variant="outline"
          size="icon"
          onClick={onRemove}
          className="text-red-500 hover:text-red-700"
        >
          <Trash2 className="w-4 h-4" />
        </Button>
      </div>

      <div className="grid grid-cols-1 gap-4">
        <div>
          <Label htmlFor={`story-title-${index}`}>
            {t("productLabels.storyTitle")} {t("required")}
          </Label>
          <Input
            id={`story-title-${index}`}
            name={`stories[${index}].title`}
            value={story.title}
            onChange={(e) => handleChange("title", e.target.value)}
            placeholder={t("productLabels.storyTitle")}
          />
        </div>

        <div>
          <Label htmlFor={`story-description-${index}`}>
            {t("productLabels.storyDescription")} {t("required")}
          </Label>
          <Textarea
            id={`story-description-${index}`}
            name={`stories[${index}].description`}
            value={story.description}
            onChange={(e) => handleChange("description", e.target.value)}
            placeholder={t("productLabels.storyDescription")}
            rows={3}
          />
        </div>

        <div>
          <Label htmlFor={`story-image-${index}`}>
            {t("productLabels.image")} {t("optional")}
          </Label>
          <ImageUpload
            name={`stories[${index}].image`}
            defaultSrc={story.image}
          />
        </div>

        <div>
          <Label htmlFor={`story-displayOrder-${index}`}>
            {t("productLabels.displayOrder")} {t("optional")}
          </Label>
          <WarnInput
            id={`story-displayOrder-${index}`}
            name={`stories[${index}].displayOrder`}
            type="number"
            value={story.displayOrder || ""}
            onChange={(e) =>
              handleChange("displayOrder", parseInt(e.target.value) || 0)
            }
            onWheel={disableScrollOnNumberInput}
            placeholder={t("productLabels.displayOrder")}
          />
        </div>
      </div>
    </Card>
  );
}
