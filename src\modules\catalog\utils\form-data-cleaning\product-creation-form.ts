import {
  buildMultilanguageFaqs,
  buildMultilanguageSections,
} from "./build-multilanguage-faqs-sections";

export default function cleanProductCreationFormData(
  formData: FormData
): FormData {
  const filteredFormData = new FormData();
  const categoryIds: string[] = [];
  const content: Array<{
    name: string;
    description: string;
    details: string;
    language: string;
  }> = [];
  const languages = ["arabic", "french", "english"];

  formData.forEach((value, key) => {
    const stringValue = value as string;

    if (key === "displayOrder") {
      if (stringValue.trim() !== "") filteredFormData.append(key, value);
    } else if (key === "categoryIds") {
      categoryIds.push(stringValue);
    } else if (
      !key.includes("_arabic") &&
      !key.includes("_french") &&
      !key.includes("_english") &&
      !key.startsWith("faqs[") &&
      !key.startsWith("sections[") &&
      typeof value === "string" &&
      value.trim() !== ""
    ) {
      filteredFormData.append(key, value);
    }
  });

  languages.forEach((lang) => {
    const nameKey = `name_${lang}`;
    const descKey = `description_${lang}`;
    const detailsKey = `details_${lang}`;

    let nameValue = "";
    let descValue = "";
    let detailsValue = "";

    if (formData.has(nameKey)) {
      nameValue = (formData.get(nameKey) as string)?.trim() || "";
    }

    if (formData.has(descKey)) {
      descValue = (formData.get(descKey) as string)?.trim() || "";
    }

    if (formData.has(detailsKey)) {
      detailsValue = (formData.get(detailsKey) as string)?.trim() || "";
    }

    if (nameValue !== "" || descValue !== "" || detailsValue !== "") {
      content.push({
        name: nameValue,
        description: descValue,
        details: detailsValue,
        language: lang.charAt(0).toUpperCase() + lang.slice(1),
      });
    }
  });

  if (content.length > 0) {
    filteredFormData.append("content", JSON.stringify(content));
  }

  categoryIds.forEach((id) => filteredFormData.append("categoryIds", id));

  // Process FAQs and sections using the new approach
  const faqs = buildMultilanguageFaqs(formData);
  const sections = buildMultilanguageSections(formData);

  // Add sections if any exist
  if (sections.length > 0) {
    filteredFormData.append("sections", JSON.stringify(sections));
  }

  // Add faqs if any exist
  if (faqs.length > 0) {
    filteredFormData.append("faqs", JSON.stringify(faqs));
  }

  return filteredFormData;
}
