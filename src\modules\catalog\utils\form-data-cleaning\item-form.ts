export default function cleanItemFormData(
  formData: FormData,
  elementId?: string,
  newImages?: File[],
  deletedImages?: string[]
): FormData {
  const filteredFormData = new FormData();
  let hasOnlineField = false;
  const BASE_URL = process.env.BACKEND_ADDRESS || "";

  formData.forEach((value, key) => {
    if (key === "online") hasOnlineField = true;

    if (!elementId) {
      // Item creation - ensure all required fields are included
      if (value instanceof File && value.size > 0) {
        filteredFormData.append(key, value);
      } else if (typeof value === "string" && value.trim() !== "") {
        // For creation, append all non-empty string values
        filteredFormData.append(key, value);
      }
    } else if (elementId && key !== "images") {
      // Item edition
      filteredFormData.append(key, value);
    }
  });

  // Handle images for creation
  if (!elementId) {
    const images = formData.getAll("images");
    images.forEach((image) => {
      if (image instanceof File && image.size > 0) {
        filteredFormData.append("images", image);
      }
    });
  }

  // Handle images for edition
  if (elementId && newImages) {
    newImages.forEach((image) => {
      filteredFormData.append("newImages", image);
    });
  }

  if (elementId && deletedImages && deletedImages.length > 0) {
    deletedImages.forEach((image) => {
      filteredFormData.append(
        "deletedImages[]",
        (image as string).replace(BASE_URL, "")
      );
    });
  }

  // Set default online value if not provided
  if (hasOnlineField === false) {
    filteredFormData.append("online", "false");
  }

  return filteredFormData;
}
