import { useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import { CustomError } from "@/utils/custom-error";
import { useTranslations } from "next-intl";
import { deleteReviewOnServerSide } from "../services/review-deletion";

export default function useReviewDeletion() {
  const queryClient = useQueryClient();
  const [alertModalIsOpen, setAlertModalIsOpen] = useState(false);
  const [warning, setWarning] = useState("");
  const t = useTranslations("warnings");
  const [isPending, setIsPending] = useState(false);
  const [reviewId, setReviewId] = useState("");

  const requestReviewDeletion = (reviewId: string) => {
    setReviewId(reviewId);
    setAlertModalIsOpen(true);
  };

  const cancelDeletion = () => {
    setAlertModalIsOpen(false);
    setReviewId("");
    if (warning !== "") setWarning("");
  };

  const deleteReview = async () => {
    if (!reviewId) return;

    setIsPending(true);

    try {
      await deleteReviewOnServerSide(reviewId);

      setAlertModalIsOpen(false);
      setReviewId("");
      if (warning !== "") setWarning("");

      // Invalidate reviews queries to refresh the list
      queryClient.invalidateQueries({
        queryKey: ["reviews"],
        exact: false,
      });
    } catch (error) {
      const customError = error as CustomError;

      if (customError.status === 404) {
        setWarning("Cette avis est introuvable.");
      } else {
        setWarning(t("serverError"));
      }
    } finally {
      setIsPending(false);
    }
  };

  return {
    isPending,
    alertModalIsOpen,
    warning,
    requestReviewDeletion,
    cancelDeletion,
    deleteReview,
  };
}
