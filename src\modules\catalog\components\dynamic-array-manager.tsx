"use client";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { PlusIcon } from "lucide-react";
import { useTranslations } from "next-intl";
import Text from "@/styles/text-styles";

interface DynamicArrayManagerProps {
  title: string;
  items: any[];
  onItemsChange: (items: any[]) => void;
  renderItem: (
    item: any,
    index: number,
    onUpdate: (updatedItem: any) => void,
    onRemove: () => void
  ) => React.ReactNode;
  createNewItem: () => any;
  className?: string;
}

export default function DynamicArrayManager({
  title,
  items,
  onItemsChange,
  renderItem,
  createNewItem,
  className = "",
}: DynamicArrayManagerProps) {
  const t = useTranslations("shared.forms.upload");

  const addItem = () => {
    const newItem = createNewItem();
    onItemsChange([...items, newItem]);
  };

  const updateItem = (index: number, updatedItem: any) => {
    const updatedItems = [...items];
    updatedItems[index] = updatedItem;
    onItemsChange(updatedItems);
  };

  const removeItem = (index: number) => {
    const updatedItems = items.filter((_, i) => i !== index);
    onItemsChange(updatedItems);
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex justify-between items-center">
        <Text textStyle="TS5" className="font-bold text-black">
          {title}
        </Text>
        <Button
          type="button"
          onClick={addItem}
          className="bg-blue text-white hover:bg-blue/90 flex items-center gap-2"
        >
          <PlusIcon className="w-4 h-4" />
          {t("productLabels.add")}
        </Button>
      </div>

      <div className="space-y-3">
        {items.map((item, index) =>
          renderItem(
            item,
            index,
            (updatedItem) => updateItem(index, updatedItem),
            () => removeItem(index)
          )
        )}
      </div>

      {items.length === 0 && (
        <Card className="p-6 text-center">
          <Text textStyle="TS6" className="text-gray-500">
            {t("productLabels.noItemsFound")}
          </Text>
        </Card>
      )}
    </div>
  );
}
