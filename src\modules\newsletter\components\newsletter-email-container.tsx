"use client";
import { HTMLAttributes } from "react";
import Text from "@/styles/text-styles";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import { Mail } from "lucide-react";
import { NewsletterEmailType } from "../types/newsletter";
import { formatDate } from "@/utils/date-formatter";

interface Props extends HTMLAttributes<"div"> {
  email: NewsletterEmailType;
  addEmailToDeletionList: (emailId: string) => void;
  removeEmailFromDeletionList: (emailId: string) => void;
}

export default function NewsletterEmailContainer({ email, ...props }: Props) {
  const handleDeletionCheck = (checked: boolean) => {
    if (checked) {
      props.addEmailToDeletionList(email.id);
    } else {
      props.removeEmailFromDeletionList(email.id);
    }
  };

  return (
    <div className="flex items-center justify-between p-2 regularL:p-4 border rounded-lg hover:bg-lightGray group">
      <div className="flex items-center gap-3 regularL:gap-4">
        <Checkbox
          onCheckedChange={handleDeletionCheck}
          className="text-blue outline-none bg-white w-5 h-5 flex-shrink-0"
        />
        <div className="flex flex-col gap-1">
          <div className="flex items-center gap-2">
            <Mail className="h-4 w-4 text-gray-500" />
            <Text textStyle="TS5" className="font-semibold text-black">
              {email.email}
            </Text>
          </div>
          <Text textStyle="TS8" className="text-gray">
            {formatDate(email.createdAt)}
          </Text>
        </div>
      </div>
    </div>
  );
}
