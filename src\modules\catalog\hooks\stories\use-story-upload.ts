import { useQueryClient } from "@tanstack/react-query";
import { useState, useRef, FormEvent } from "react";
import { useTranslations } from "next-intl";
import { useToast } from "@/hooks/use-toast";
import { CustomError } from "@/utils/custom-error";
import { StoryType } from "@/modules/catalog/types/products";

import uploadStoryToServerSide from "../../services/stories/story-upload";
import cleanStoryFormData from "../../utils/form-data-cleaning/story-form";
import { validateStoryData } from "../../validation/stories/validate-story-data";

interface UseStoryUploadProps {
  productId: string;
  story?: StoryType | null;
  isEditing?: boolean;
  onSuccess?: () => void;
}

export default function useStoryUpload({
  productId,
  story,
  isEditing = false,
  onSuccess,
}: UseStoryUploadProps) {
  const queryClient = useQueryClient();
  const t = useTranslations("warnings");
  const { toast } = useToast();
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingStory, setEditingStory] = useState<StoryType | null>(null);
  const [warning, setWarning] = useState("");
  const [isPending, setIsPending] = useState(false);
  const formRef = useRef<HTMLFormElement>(null);

  const handleCreateStory = () => {
    setIsCreateDialogOpen(true);
  };

  const handleEditStory = (story: StoryType) => {
    setEditingStory(story);
  };

  const handleCreateSuccess = () => {
    setIsCreateDialogOpen(false);
    onSuccess?.();
  };

  const handleEditSuccess = () => {
    setEditingStory(null);
    onSuccess?.();
  };

  const handleCloseCreateDialog = () => {
    setIsCreateDialogOpen(false);
  };

  const handleCloseEditDialog = () => {
    setEditingStory(null);
  };

  async function submitStory(event: FormEvent) {
    if (!formRef.current) return;

    setIsPending(true);
    event.preventDefault();

    try {
      const formData = new FormData(formRef.current);
      const cleanedFormData = cleanStoryFormData(
        formData,
        productId,
        isEditing && story ? story.id : undefined
      );

      validateStoryData(cleanedFormData, isEditing);
      setWarning("");

      await uploadStoryToServerSide(
        cleanedFormData,
        isEditing && story ? story.id : undefined
      );

      queryClient.invalidateQueries({
        queryKey: ["product", productId],
        exact: false,
      });

      toast({
        title: "Succès",
        description: isEditing
          ? "Histoire mise à jour avec succès"
          : "Histoire créée avec succès",
      });

      onSuccess?.();
    } catch (error) {
      const customError = error as CustomError;
      if (customError.status === 500) {
        setWarning(t("serverError"));
        toast({
          title: t("warning"),
          description: t("serverError"),
          variant: "destructive",
        });
      } else if (customError.status === 400) {
        if (customError.message === "Missed Data!") {
          setWarning(t("upload.missedData"));
          toast({
            title: t("warning"),
            description: t("upload.missedData"),
            variant: "destructive",
          });
        } else {
          setWarning(t("upload.invalidFormat"));
          toast({
            title: t("warning"),
            description: t("upload.invalidFormat"),
            variant: "destructive",
          });
        }
      } else if (customError.status === 404) {
        const errorMessage = isEditing
          ? "Histoire ou produit non trouvé"
          : "Produit non trouvé";
        setWarning(errorMessage);
        toast({
          title: t("warning"),
          description: errorMessage,
          variant: "destructive",
        });
      }
    } finally {
      setIsPending(false);
    }
  }

  return {
    submitStory,
    isPending,
    isCreateDialogOpen,
    editingStory,
    handleCreateStory,
    handleEditStory,
    handleCreateSuccess,
    handleEditSuccess,
    handleCloseCreateDialog,
    handleCloseEditDialog,
    warning,
    setWarning,
    formRef,
  };
}
