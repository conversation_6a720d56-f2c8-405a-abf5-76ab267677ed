import { CustomError } from "@/utils/custom-error";

export function validateCategoryData(formData: FormData): void {
  const requiredFields: string[] = ["name"];
  const missingFields: string[] = [];

  requiredFields.forEach((field) => {
    if (!formData.has(field) || formData.get(field) === "") {
      missingFields.push(field);
    }
  });

  if (missingFields.length > 0) {
    throw new CustomError(`Missed Data!`, 400);
  }

  const displayOrder = formData.get("displayOrder");
  if (displayOrder !== null && typeof displayOrder === "string") {
    if (parseFloat(displayOrder) < 1) {
      throw new CustomError(`Invalid Data!`, 400);
    }
  }
}
