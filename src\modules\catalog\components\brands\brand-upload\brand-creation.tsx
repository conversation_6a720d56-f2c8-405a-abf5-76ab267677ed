import Text from "@/styles/text-styles";
import ImageUpload from "@/modules/catalog/components/images-management/image-upload";
import { useTranslations } from "next-intl";
import FormSubmission from "../../form-submission";
import { useRouter } from "next/navigation";
import { Label } from "@/components/ui/label";
import useMultilanguageBrandCreation from "@/modules/catalog/hooks/brands/use-brand-creation";
import MultilanguageSeoContent from "@/modules/seo/components/seo-meta-content/multilanguage-seo-content";
import useSeoMetaData from "@/modules/seo/hooks/use-seo-meta-data";
import { usePreviousUrl } from "@/hooks/urls-management/use-previous-url";
import { useState } from "react";
import MultilanguageFormFields from "./multilanguage-form-fields";
import LanguageTabs from "./language-tabs";
import WarnInput from "@/components/input/warn-input";
import { disableScrollOnNumberInput } from "@/utils/number-input";
import { Language } from "@/modules/seo/types/multilanguage-seo";

export default function BrandCreation() {
  const t = useTranslations("BrandsManagement");
  const uploadContent = useTranslations("shared.forms.upload");

  const [activeLanguage, setActiveLanguage] = useState("french");

  const router = useRouter();
  const previousUrl = usePreviousUrl();
  const {
    metaContent,
    handleMetaTitleChange,
    handleMetaDescriptionChange,
    addNewKeyword,
    removeKeyword,
    getMultilanguageMetaContent,
  } = useSeoMetaData(activeLanguage as Language);

  const { formRef, submitBrand, warning, isPending } =
    useMultilanguageBrandCreation(getMultilanguageMetaContent);

  const cancelSubmission = () => {
    if (previousUrl && previousUrl.startsWith("/brands"))
      router.push(previousUrl);
    else router.push("/brands");
  };

  return (
    <FormSubmission
      submit={uploadContent("save")}
      cancel={uploadContent("cancel")}
      isPending={isPending}
      onCancel={cancelSubmission}
      onSubmit={submitBrand}
    >
      <form ref={formRef} className="flex flex-col extraXL:flex-row gap-4">
        <div className="basis-[70%] regularL:order-1 order-2 flex flex-col gap-5">
          {/* Language Content Card */}
          <div className="rounded-2xl border border-lightGray p-7 bg-white space-y-7">
            <LanguageTabs
              options={[
                { key: "arabic", value: t("languages.arabic") },
                { key: "french", value: t("languages.french") },
                { key: "english", value: t("languages.english") },
              ]}
              onSelect={(language) => {
                setActiveLanguage(language);
              }}
              selectedValue={activeLanguage}
            />
            <Text textStyle="TS4" className="font-bold text-black">
              {t("brandContent")}
            </Text>
            <div className="text-red self-center">{warning}</div>

            <div className="w-full">
              <div
                style={{
                  display: activeLanguage === "arabic" ? "block" : "none",
                }}
              >
                <MultilanguageFormFields
                  multilanguage={true}
                  language="arabic"
                />
              </div>

              <div
                style={{
                  display: activeLanguage === "french" ? "block" : "none",
                }}
              >
                <MultilanguageFormFields
                  multilanguage={true}
                  language="french"
                />
              </div>

              <div
                style={{
                  display: activeLanguage === "english" ? "block" : "none",
                }}
              >
                <MultilanguageFormFields
                  multilanguage={true}
                  language="english"
                />
              </div>
            </div>
          </div>
          {/* Images Card */}
          <div className="rounded-2xl border border-lightGray p-7 bg-white space-y-7">
            <Text textStyle="TS4" className="font-bold text-black">
              {t("brandImages")}
            </Text>
            <div className="w-full flex flex-col space-y-2">
              <Label>{uploadContent("brandLabels.image")}</Label>
              <ImageUpload name="image" />
            </div>
          </div>

          {/* Display Order Card */}
          <div className="rounded-2xl border border-lightGray p-7 bg-white space-y-7">
            <Text textStyle="TS4" className="font-bold text-black">
              {uploadContent("brandLabels.displayOrder")}
            </Text>
            <div className="w-full flex flex-col space-y-2">
              <Label htmlFor="displayOrder">
                {`${uploadContent("brandLabels.displayOrder")} ${uploadContent(
                  "optional"
                )}`}
              </Label>
              <WarnInput
                id="displayOrder"
                name="displayOrder"
                type="number"
                onWheel={disableScrollOnNumberInput}
                warning=""
                placeholder="10"
              />
            </div>
          </div>
        </div>
        <div className="basis-[30%] order-2 space-y-4">
          <div className="rounded-2xl border border-lightGray p-7 bg-white space-y-5">
            <MultilanguageSeoContent
              metaContent={metaContent}
              activeLanguage={activeLanguage as Language}
              changeMetaTitle={handleMetaTitleChange}
              changeMetaDescription={handleMetaDescriptionChange}
              addNewKeyword={addNewKeyword}
              removeKeyword={removeKeyword}
            />
          </div>
        </div>
      </form>
    </FormSubmission>
  );
}
