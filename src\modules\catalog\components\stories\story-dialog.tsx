"use client";
import { useTranslations } from "next-intl";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>Title,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import Text from "@/styles/text-styles";
import { disableScrollOnNumberInput } from "@/utils/number-input";
import { StoryType } from "@/modules/catalog/types/products";
import ImageUpload from "../images-management/image-upload";
import useStoryUpload from "../../hooks/stories/use-story-upload";

interface StoryDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  productId: string;
  story?: StoryType;
  isEditing?: boolean;
  nextDisplayOrder?: number;
}

export default function StoryDialog({
  isO<PERSON>,
  onClose,
  onSuccess,
  productId,
  story,
  isEditing = false,
  nextDisplayOrder = 1,
}: StoryDialogProps) {
  const t = useTranslations("shared.forms.upload");

  const { isPending, submitStory, warning, formRef } = useStoryUpload({
    productId,
    story,
    isEditing,
    onSuccess: () => {
      onSuccess();
      handleClose();
    },
  });

  const handleClose = () => {
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? "Modifier l'histoire" : "Ajouter une histoire"}
          </DialogTitle>
        </DialogHeader>

        <form ref={formRef} onSubmit={submitStory} className="space-y-4">
          {warning && (
            <Text textStyle="TS7" className="text-red mt-1">
              {warning}
            </Text>
          )}
          <div>
            <Label htmlFor="story-title">
              {t("productLabels.storyTitle")} {t("required")}
            </Label>
            <Input
              id="title"
              name="title"
              defaultValue={isEditing && story ? story.title : ""}
              placeholder={t("productLabels.storyTitle")}
            />
          </div>

          <div>
            <Label htmlFor="story-description">
              {t("productLabels.storyDescription")} {t("required")}
            </Label>
            <Textarea
              id="description"
              name="description"
              defaultValue={isEditing && story ? story.description : ""}
              placeholder={t("productLabels.storyDescription")}
              rows={4}
            />
          </div>

          <div>
            <Label htmlFor="image">
              {t("productLabels.image")} {t("required")}
            </Label>
            <ImageUpload
              name="image"
              defaultSrc={isEditing && story ? story.image : null}
            />
          </div>

          <div>
            <Label htmlFor="displayOrder">
              {t("productLabels.displayOrder")} {t("optional")}
            </Label>
            <Input
              id="displayOrder"
              name="displayOrder"
              type="number"
              defaultValue={
                isEditing && story
                  ? story.displayOrder?.toString() || "1"
                  : nextDisplayOrder.toString()
              }
              onWheel={disableScrollOnNumberInput}
              placeholder={t("productLabels.displayOrder")}
            />
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isPending}
            >
              {t("cancel")}
            </Button>
            <Button
              type="submit"
              disabled={isPending}
              className="bg-blue text-white hover:bg-blue/90"
            >
              {isPending ? "Enregistrement..." : t("save")}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
