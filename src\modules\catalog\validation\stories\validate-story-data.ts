import { CustomError } from "@/utils/custom-error";

export function validateStoryData(
  formData: FormData,
  isEditing: boolean = false
): void {
  const requiredFields: string[] = ["title", "image"];
  const missingFields: string[] = [];

  requiredFields.forEach((field) => {
    if (!formData.has(field) || formData.get(field) === "") {
      missingFields.push(field);
    }
  });

  if (missingFields.length > 0) {
    throw new CustomError(`Missed Data!`, 400);
  }

  const title = formData.get("title") as string;
  const image = formData.get("image") as File;
  const description = formData.get("description") as string;
  const displayOrder = formData.get("displayOrder") as string;

  // Validate title
  if (title && title.trim().length > 100) {
    throw new CustomError(`Invalid Data`, 400);
  }

  // Validate image for creation
  if (!isEditing && (!image || !(image instanceof File) || image.size === 0)) {
    throw new CustomError(`Invalid Data`, 400);
  }

  // Validate image type and size if provided
  if (image && image instanceof File && image.size > 0) {
    if (!image.type.startsWith("image/")) {
      throw new CustomError(`Invalid Data`, 400);
    }
    if (image.size > 1 * 1024 * 1024) {
      throw new CustomError(`Invalid Data`, 400);
    }
  }

  if (description && description.trim().length === 0) {
    throw new CustomError(`Invalid Data`, 400);
  }

  if (displayOrder && displayOrder.trim()) {
    const orderNum = parseInt(displayOrder);
    if (isNaN(orderNum) || orderNum < 1 || orderNum > 999) {
      throw new CustomError(`Invalid Data`, 400);
    }
  }
}
