import {
  SeoMetaContentType,
  SeoMetaContentTypeInResponse,
} from "@/modules/seo/types";

export interface ProductType {
  id: string;
  categoryIds: string[];
  brand: { id: string; name: string };
  name: string;
  description: string | null;
  details: string | null;
  displayOrder: number;
  metaContent: SeoMetaContentType | null;
  items: ItemType[];
  slug: string;
  sections?: SectionType[];
  faqs?: FaqType[];
  ingredients?: IngredientType[];
  stories?: StoryType[];
}

export interface ProductInResponseType {
  id: string;
  slug: string;
  categoryIds: string[];
  brand: { id: string; name: string };
  name: string;
  description: string | null;
  details: string | null;
  displayOrder: number;
  metaContent: SeoMetaContentTypeInResponse;
  items: ItemInResponseType[];
  sections?: SectionType[];
  faqs?: FaqType[];
  ingredients?: IngredientType[];
  stories?: StoryType[];
}

export interface ItemType {
  id: string;
  name: string;
  barcode: string;
  reference: string;
  inStock: boolean;
  quantity: number;
  image: string;
  images: string[];
  prices: PriceType[];
  variations: VariationType[];
  online: boolean;
}

export interface ProductItemType extends ItemType {
  name: string;
}

export interface ItemInResponseType {
  id: string;
  barcode: string;
  reference: string;
  inStock: boolean;
  image: string;
  images: string[];
  quantity: number;
  prices: PriceInResponseType[];
  variation?: VariationType[];
  online: boolean;
}

export interface PriceType {
  currency: string;
  realPrice: number;
  promotionalPrice: number;
}

export interface PriceInResponseType {
  regularPrice: number;
  promotionalPrice: number;
  currency: string;
}

export interface VariationType {
  name: string;
  value: string;
}

export interface SectionType {
  id?: string;
  title: string;
  description: string;
  displayOrder: number;
}

export interface FaqType {
  id?: string;
  question: string;
  answer: string;
  displayOrder: number;
}

export interface MultilanguageFaqContent {
  question: string;
  answer: string;
  language: string;
}

export interface MultilanguageFaq {
  id?: string;
  displayOrder: number;
  content: MultilanguageFaqContent[];
}

export interface MultilanguageSectionContent {
  title: string;
  description: string;
  language: string;
}

export interface MultilanguageSection {
  id?: string;
  displayOrder: number;
  content: MultilanguageSectionContent[];
}

export interface IngredientType {
  id?: string;
  title: string;
  description: string;
  image?: string;
  displayOrder?: number;
}

export interface IngredientInResponseType {
  id?: string;
  title: string;
  description: string;
  image?: string;
  displayOrder?: number;
}

// API Request type for ingredient registration
export interface IngredientRegistrationRequest {
  productId: string;
  title: string;
  description: string;
  image?: string;
  displayOrder: number;
}

// API Response type for ingredient registration
export interface IngredientRegistrationResponse {
  id: string;
  title: string;
  description: string;
  image?: string;
  displayOrder: number;
}

// API Request type for ingredient update
export interface IngredientUpdateRequest {
  productId: string;
  title: string;
  description: string;
  image?: string;
  displayOrder: number;
}

// API Response type for ingredient update
export interface IngredientUpdateResponse {
  id: string;
  title: string;
  description: string;
  image?: string;
  displayOrder: number;
}

export interface StoryType {
  id?: string;
  title: string;
  description: string;
  image?: string;
  displayOrder?: number;
}

export interface StoryInResponseType {
  id?: string;
  title: string;
  description: string;
  image?: string;
  displayOrder?: number;
}

// API Request type for story registration
export interface StoryRegistrationRequest {
  productId: string;
  title: string;
  description: string;
  image?: string;
  displayOrder: number;
}

// API Response type for story registration
export interface StoryRegistrationResponse {
  id: string;
  title: string;
  description: string;
  image?: string;
  displayOrder: number;
}

// API Request type for story update
export interface StoryUpdateRequest {
  productId: string;
  title: string;
  description: string;
  image?: string;
  displayOrder: number;
}

// API Response type for story update
export interface StoryUpdateResponse {
  id: string;
  title: string;
  description: string;
  image?: string;
  displayOrder: number;
}

export interface ProductUploadType {
  name: string;
  description?: string;
  details?: string;
  brandId?: string;
  displayOrder?: number;
  categoryIds: string[];
  sections?: SectionType[];
  faqs?: FaqType[];
  ingredients?: IngredientType[];
  stories?: StoryType[];
  items?: ItemUploadType[];
  metaContent?: SeoMetaContentTypeInResponse;
}

export interface ItemUploadType {
  productId: string;
  barcode: string;
  online: boolean;
  image?: string;
  images?: string[];
  quantity: number;
  price: number;
}

// API Request type for item registration
export interface ItemRegistrationRequest {
  productId: string;
  barcode: string;
  online: boolean;
  image?: string;
  images?: string[];
  quantity: number;
  price: number;
}

// API Response type for item registration
export interface ItemRegistrationResponse {
  id: string;
  barcode: string;
  online: boolean;
  prices: PriceInResponseType[];
  image: string;
  images: string[];
  quantity: number;
}

export interface PriceUploadType {
  regularPrice: number;
  promotionalPrice: number;
  currency: string;
}

export interface ErrorDataResponse {
  message: string;
  code: string;
}
