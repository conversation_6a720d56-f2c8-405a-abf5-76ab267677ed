import { POST, PATCH } from "@/lib/http-methods";
import extractJWTokens from "@/modules/auth/utils/jwt/extract-tokens";
import { AxiosError } from "axios";
import { refreshToken } from "@/modules/auth/services/refresh-token";
import { CustomError } from "@/utils/custom-error";
import {
  StoryRegistrationResponse,
  ErrorDataResponse,
} from "../../types/products";

export default async function uploadStoryToServerSide(
  formData: FormData,
  storyId?: string
): Promise<StoryRegistrationResponse> {
  const { access } = extractJWTokens();
  const header = {
    Authorization: `Bearer ${access}`,
  };

  try {
    if (!storyId) {
      // Story creation
      const endpoint = "/products/stories/register";

      const res = await POST(
        `${process.env.BACKEND_ADDRESS}${endpoint}`,
        header,
        formData
      );

      return res.data as StoryRegistrationResponse;
    } else {
      // Story edition
      const endpoint = `/products/stories/${storyId}`;

      const res = await PATCH(
        `${process.env.BACKEND_ADDRESS}${endpoint}`,
        header,
        formData
      );

      return res.data as StoryRegistrationResponse;
    }
  } catch (error) {
    const axiosError = error as AxiosError<ErrorDataResponse>;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() =>
        uploadStoryToServerSide(formData, storyId)
      );

      if (!res) throw new CustomError("Unauthorized", 401);
      return res;
    }

    if (axiosError.response?.status === 400) {
      throw new CustomError("Invalid Data", 400);
    }

    if (axiosError.response?.status === 404) {
      throw new CustomError("Story or product not found", 404);
    }

    throw new CustomError("Server Error!", 500);
  }
}
