import { GET } from "@/lib/http-methods";
import { PaginationType } from "@/types/pagination";
import { refreshToken } from "@/modules/auth/services/refresh-token";
import { AxiosError } from "axios";
import extractJWTokens from "@/modules/auth/utils/jwt/extract-tokens";
import { NewsletterEmailInResponseType } from "../types/newsletter";
import { castToNewsletterEmailType } from "../utils/data-management/types-casting/newsletter";
import { CustomError } from "@/utils/custom-error";

export async function retrieveNewsletterEmailsFromServerSide({
  page,
  limit,
  searchedEmails,
}: {
  page: number;
  limit?: number;
  searchedEmails?: string;
}) {
  const { access } = extractJWTokens();
  const headers = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const endpoint = "/newsletter/emails/dashboard";
    const queryParams = new URLSearchParams();
    queryParams.append("page", page.toString());
    if (limit) queryParams.append("limit", limit.toString());
    if (searchedEmails) queryParams.append("search", searchedEmails);

    const response = await GET(
      `${process.env.BACKEND_ADDRESS}${endpoint}?${queryParams.toString()}`,
      headers
    );

    return {
      pagination: response.data.pagination as PaginationType,
      data: response.data.data.map((email: NewsletterEmailInResponseType) =>
        castToNewsletterEmailType(email)
      ),
    };
  } catch (error) {
    const axiosError = error as AxiosError;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() =>
        retrieveNewsletterEmailsFromServerSide({ page, limit, searchedEmails })
      );

      if (!res) throw new CustomError("Unauthorized", 401);

      return res;
    }

    throw new CustomError("Server Error!", 500);
  }
}
