import { CustomError } from "@/utils/custom-error";

export function validateProductEditionData(formData: FormData): void {
  const content = formData.get("content") as string;
  const categoryIds = formData.getAll("categoryIds");
  const displayOrder = formData.get("displayOrder") as string;

  if (!content || content.trim() === "") {
    throw new CustomError("Missed Data!", 400);
  }

  if (!categoryIds || categoryIds.length === 0 || categoryIds[0] === "") {
    throw new CustomError("Missed Data!", 400);
  }

  if (displayOrder && displayOrder.trim() !== "") {
    const orderNumber = parseInt(displayOrder, 10);
    if (isNaN(orderNumber) || orderNumber < 1) {
      throw new CustomError("Invalid Data!", 400);
    }
  }
}
