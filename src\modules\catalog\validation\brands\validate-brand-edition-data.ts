import { CustomError } from "@/utils/custom-error";

export function validateBrandEditionData(formData: FormData): void {
  const content = formData.get("content") as string;
  const displayOrder = formData.get("displayOrder") as string;

  if (!content || content.trim() === "") {
    throw new CustomError("Missed Data!", 400);
  }

  if (displayOrder && displayOrder.trim() !== "") {
    const orderNumber = parseInt(displayOrder, 10);
    if (isNaN(orderNumber) || orderNumber < 1) {
      throw new CustomError("Invalid Data!", 400);
    }
  }
}
