"use client";
import { useTranslations } from "next-intl";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { AlertDialog } from "@/components/ui/alert-dialog";
import { PlusIcon } from "lucide-react";
import Text from "@/styles/text-styles";
import ModalDialog from "@/components/modal-dialog";
import { StoryType } from "@/modules/catalog/types/products";
import useStoryUpload from "../../hooks/stories/use-story-upload";
import useStoryDelete from "../../hooks/stories/use-story-delete";
import StoryDialog from "./story-dialog";
import StoryCard from "./story-card";

interface StoriesListProps {
  stories: StoryType[];
  productId: string;
  onStoriesRefetch: () => void;
  className?: string;
}

export default function StoriesList({
  stories,
  productId,
  onStoriesRefetch,
  className = "",
}: StoriesListProps) {
  const t = useTranslations("shared.forms.upload");
  const deleteTranslations = useTranslations("ProductsManagement.dialog");

  const {
    isCreateDialogOpen,
    editingStory,
    handleCreateStory,
    handleEditStory,
    handleCreateSuccess,
    handleEditSuccess,
    handleCloseCreateDialog,
    handleCloseEditDialog,
  } = useStoryUpload({
    productId,
    onSuccess: onStoriesRefetch,
  });

  const {
    deletingStory,
    isPending: isDeleting,
    handleDeleteStory,
    handleConfirmDelete,
    handleCancelDelete,
  } = useStoryDelete({
    productId,
    onSuccess: onStoriesRefetch,
  });

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex justify-between items-center">
        <Text textStyle="TS5" className="font-bold text-black">
          {t("productLabels.stories")}
        </Text>
        <Button
          type="button"
          onClick={handleCreateStory}
          className="bg-blue text-white hover:bg-blue/90 flex items-center gap-2"
        >
          <PlusIcon className="w-4 h-4" />
          {t("productLabels.add")}
        </Button>
      </div>

      <div className="space-y-3">
        {stories.map((story, index) => (
          <StoryCard
            key={story.id || index}
            story={story}
            index={index}
            onEdit={handleEditStory}
            onDelete={handleDeleteStory}
          />
        ))}
      </div>

      {stories.length === 0 && (
        <Card className="p-6 text-center">
          <Text textStyle="TS6" className="text-gray-500">
            {t("productLabels.noItemsFound")}
          </Text>
        </Card>
      )}

      {/* Create Story Dialog */}
      <StoryDialog
        isOpen={isCreateDialogOpen}
        onClose={handleCloseCreateDialog}
        onSuccess={handleCreateSuccess}
        productId={productId}
        nextDisplayOrder={stories.length + 1}
      />

      {/* Edit Story Dialog */}
      {editingStory && (
        <StoryDialog
          isOpen={true}
          onClose={handleCloseEditDialog}
          onSuccess={handleEditSuccess}
          productId={productId}
          story={editingStory}
          isEditing={true}
        />
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!deletingStory} onOpenChange={handleCancelDelete}>
        {deletingStory && (
          <ModalDialog
            title={deleteTranslations("title")}
            details={`Êtes-vous sûr de vouloir supprimer l'histoire "${deletingStory.title}" ?`}
            cancel={deleteTranslations("cancel")}
            confirm={deleteTranslations("confirm")}
            isPending={isDeleting}
            onCancel={handleCancelDelete}
            onConfirm={handleConfirmDelete}
            theme="red"
          />
        )}
      </AlertDialog>
    </div>
  );
}
