import { PATCH, POST } from "@/lib/http-methods";
import extractJWTokens from "@/modules/auth/utils/jwt/extract-tokens";
import { AxiosError } from "axios";
import { refreshToken } from "@/modules/auth/services/refresh-token";
import { CustomError } from "@/utils/custom-error";
import {
  ErrorDataResponse,
  ItemRegistrationResponse,
} from "../../types/products";

export default async function uploadItemToServerSide(
  elementData: FormData,
  elementId?: string
): Promise<ItemRegistrationResponse | { ok: boolean; status: number }> {
  const { access } = extractJWTokens();
  const header = {
    Authorization: `Bearer ${access}`,
  };

  try {
    if (!elementId) {
      // Item creation
      const endpoint = "/products/item/register";

      const res = await POST(
        `${process.env.BACKEND_ADDRESS}${endpoint}`,
        header,
        elementData
      );

      // Return the response data which should match ItemRegistrationResponse
      return res.data as ItemRegistrationResponse;
    } else {
      // Item edition
      const endpoint = "/products/item";

      await PATCH(
        `${process.env.BACKEND_ADDRESS}${endpoint}/${elementId}`,
        header,
        elementData
      );

      return {
        ok: true,
        status: 204,
      };
    }
  } catch (error) {
    const axiosError = error as AxiosError<ErrorDataResponse>;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() =>
        uploadItemToServerSide(elementData, elementId)
      );

      // Unauthorized user error is already handled by the user hook
      if (!res) throw new CustomError("Unauthorized", 401);

      return res;
    } else if (axiosError.response?.status === 404) {
      throw new CustomError("This product is not found!", 404, "P6001");
    } else if (axiosError.response?.status === 400) {
      throw new CustomError(
        axiosError.response?.data.message || "Invalid Data!",
        400,
        axiosError.response?.data.code || "P1000"
      );
    } else if (axiosError.response?.status === 500) {
      throw new CustomError("Server Error!", 500, "P1001");
    }

    throw new CustomError("Server Error!", 500, "P1001");
  }
}
