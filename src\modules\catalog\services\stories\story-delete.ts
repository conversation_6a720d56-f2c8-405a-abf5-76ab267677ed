import { DELETE } from "@/lib/http-methods";
import extractJWTokens from "@/modules/auth/utils/jwt/extract-tokens";
import { AxiosError } from "axios";
import { refreshToken } from "@/modules/auth/services/refresh-token";
import { CustomError } from "@/utils/custom-error";
import { StoryType } from "../../types/products";

export default async function deleteStoryOnServerSide(
  storyToDelete: StoryType
): Promise<void> {
  const { access } = extractJWTokens();
  const header = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const endpoint = `/products/stories/${storyToDelete.id}`;

    await DELETE(`${process.env.BACKEND_ADDRESS}${endpoint}`, header);
  } catch (error) {
    const axiosError = error as AxiosError;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() =>
        deleteStoryOnServerSide(storyToDelete)
      );

      if (!res) throw new CustomError("Unauthorized", 401);
      return res;
    }

    if (axiosError.response?.status === 400) {
      throw new CustomError("Invalid Data", 400);
    }

    if (axiosError.response?.status === 404) {
      throw new CustomError("Story or product not found", 404);
    }

    throw new CustomError("Server Error!", 500);
  }
}
