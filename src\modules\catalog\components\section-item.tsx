"use client";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Card } from "@/components/ui/card";
import { Trash2 } from "lucide-react";
import { useTranslations } from "next-intl";
import Text from "@/styles/text-styles";
import WarnInput from "@/components/input/warn-input";
import { disableScrollOnNumberInput } from "@/utils/number-input";
import { Language } from "@/modules/seo/types/multilanguage-seo";

interface MultilanguageSectionContent {
  title: string;
  description: string;
  language: string;
}

interface MultilanguageSection {
  id?: string;
  displayOrder: number;
  content: MultilanguageSectionContent[];
}

interface SectionItemProps {
  section: MultilanguageSection;
  index: number;
  onUpdate: (section: MultilanguageSection) => void;
  onRemove: () => void;
  activeLanguage: Language;
}

export default function SectionItem({
  section,
  index,
  onUpdate,
  onRemove,
  activeLanguage,
}: SectionItemProps) {
  const t = useTranslations("shared.forms.upload");

  const getCurrentLanguageContent = () => {
    const languageMap = {
      arabic: "Arabic",
      french: "French",
      english: "English",
    };

    if (!section.content || !Array.isArray(section.content)) {
      return {
        title: "",
        description: "",
        language: languageMap[activeLanguage],
      };
    }

    return (
      section.content.find(
        (content) => content.language === languageMap[activeLanguage]
      ) || { title: "", description: "", language: languageMap[activeLanguage] }
    );
  };

  const handleContentChange = (
    field: "title" | "description",
    value: string
  ) => {
    const languageMap = {
      arabic: "Arabic",
      french: "French",
      english: "English",
    };

    const updatedContent = section.content ? [...section.content] : [];
    const currentLangContent = languageMap[activeLanguage];
    const existingIndex = updatedContent.findIndex(
      (content) => content.language === currentLangContent
    );

    if (existingIndex >= 0) {
      updatedContent[existingIndex] = {
        ...updatedContent[existingIndex],
        [field]: value,
      };
    } else {
      updatedContent.push({
        title: field === "title" ? value : "",
        description: field === "description" ? value : "",
        language: currentLangContent,
      });
    }

    onUpdate({
      ...section,
      content: updatedContent,
    });
  };

  const handleDisplayOrderChange = (value: number) => {
    onUpdate({
      ...section,
      displayOrder: value,
    });
  };

  return (
    <Card className="p-4 space-y-4">
      {section.id && (
        <input
          type="hidden"
          name={`sections[${index}].id`}
          value={section.id}
        />
      )}
      <div className="flex justify-between items-center">
        <Text textStyle="TS6" className="font-medium">
          {t("productLabels.section")} {index + 1}
        </Text>
        <Button
          type="button"
          variant="outline"
          size="icon"
          onClick={onRemove}
          className="text-red-500 hover:text-red-700"
        >
          <Trash2 className="w-4 h-4" />
        </Button>
      </div>

      {["arabic", "french", "english"].map((lang) => {
        const langKey = lang.charAt(0).toUpperCase() + lang.slice(1);
        const langContent = (section.content || []).find(
          (content) => content.language === langKey
        ) || { title: "", description: "", language: langKey };

        return (
          <div
            key={`${lang}-${section.id || index}`}
            style={{ display: activeLanguage === lang ? "block" : "none" }}
            className="grid grid-cols-1 gap-4"
          >
            <div>
              <Label htmlFor={`section-title-${index}-${lang}`}>
                {t("productLabels.sectionTitle")} {langKey} {t("required")}
              </Label>
              <Input
                key={`section-title-${index}-${lang}-${section.id || "new"}`}
                id={`section-title-${index}-${lang}`}
                name={`sections[${index}].title_${lang}`}
                defaultValue={langContent.title || ""}
                onChange={(e) => handleContentChange("title", e.target.value)}
                placeholder={t("productLabels.sectionTitle")}
              />
            </div>

            <div>
              <Label htmlFor={`section-description-${index}-${lang}`}>
                {t("productLabels.sectionDescription")} {langKey}{" "}
                {t("required")}
              </Label>
              <Textarea
                key={`section-description-${index}-${lang}-${
                  section.id || "new"
                }`}
                id={`section-description-${index}-${lang}`}
                name={`sections[${index}].description_${lang}`}
                defaultValue={langContent.description || ""}
                onChange={(e) =>
                  handleContentChange("description", e.target.value)
                }
                placeholder={t("productLabels.sectionDescription")}
                rows={3}
              />
            </div>
          </div>
        );
      })}

      <div>
        <Label htmlFor={`section-displayOrder-${index}`}>
          {t("productLabels.displayOrder")} {t("optional")}
        </Label>
        <WarnInput
          id={`section-displayOrder-${index}`}
          name={`sections[${index}].displayOrder`}
          type="number"
          value={section.displayOrder || ""}
          onChange={(e) =>
            handleDisplayOrderChange(parseInt(e.target.value) || 0)
          }
          onWheel={disableScrollOnNumberInput}
          placeholder={t("productLabels.displayOrder")}
        />
      </div>
    </Card>
  );
}
