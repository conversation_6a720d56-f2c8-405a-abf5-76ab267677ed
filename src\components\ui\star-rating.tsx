"use client";

import { cn } from "@/lib/utils";
import { Star } from "lucide-react";
import { useTranslations } from "next-intl";
import { HTMLAttributes } from "react";

export interface StarRatingProps extends HTMLAttributes<HTMLDivElement> {
  rating: number;
  reviewCount?: number;
  size?: number;
  maxStars?: number;
  filledClassName?: string;
  emptyClassName?: string;
  showReviewCount?: boolean;
  reviewCountClassName?: string;
}

export function StarRating({
  rating,
  reviewCount,
  size = 14,
  maxStars = 5,
  className,
  filledClassName = "text-orange-500 fill-orange-500",
  emptyClassName = "text-gray-300",
  showReviewCount = false,
  reviewCountClassName = "text-gray text-xs underline",
  ...props
}: StarRatingProps) {
  const t = useTranslations("reviews");
  return (
    <div className={cn("flex items-center gap-3", className)} {...props}>
      <div className="flex">
        {[...Array(maxStars)].map((_, i) => (
          <Star
            key={i}
            size={size}
            className={
              i < Math.floor(rating) ? filledClassName : emptyClassName
            }
          />
        ))}
      </div>
      {showReviewCount && reviewCount !== undefined && (
        <span className={reviewCountClassName}>
          {reviewCount} {t("reviews")}
        </span>
      )}
    </div>
  );
}
