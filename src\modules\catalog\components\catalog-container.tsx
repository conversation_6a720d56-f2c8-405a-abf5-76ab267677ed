import { HTMLAttributes, useEffect, useState } from "react";
import { cn } from "@/lib/utils";
import Text from "@/styles/text-styles";
import { useTranslations } from "next-intl";
import Image from "next/image";
import BigXMark from "@assets/icons/big-x-mark";
import EditionIcon from "@assets/icons/management/edition-icon";
import { CatalogElementType } from "../types";
import { Checkbox } from "@/components/ui/checkbox";

interface Props extends HTMLAttributes<"div"> {
  catalog: {
    name: string;
    id: string;
    description?: string;
    image: string | null;
  };
  type: CatalogElementType;
  onEdit: () => void;
  addCatalogToDeletionList: (catalogId: string) => void;
  removeCatalogFromDeletionList: (catalogId: string) => void;
}

export default function CatalogCotainer({ catalog, type, ...props }: Props) {
  let translationName = "";
  const [catalogImage, setCatalogImage] = useState("/not-found/image.png");

  useEffect(() => {
    if (catalog.image) setCatalogImage(catalog.image);
  }, [catalog]);

  switch (type) {
    case "brand":
      translationName = "BrandsManagement";
      break;
    case "category":
      translationName = "CategoriesManagement";
      break;
      break;
    case "product":
      translationName = "ProductsManagement";
      break;
    case "item":
      translationName = "ItemsManagement";
      break;
    case "palette":
      translationName = "PalettesManagement";
      break;
  }

  const t = useTranslations(translationName);

  const handleDeletionCheck = (checked: boolean) => {
    if (checked) {
      props.addCatalogToDeletionList(catalog.id);
    } else props.removeCatalogFromDeletionList(catalog.id);
  };

  return (
    <div
      className={cn(
        "bg-white border group rounded-[6px] h-[300px] w-[300px] flex flex-col relative overflow-hidden",
        props.className
      )}
    >
      <Checkbox
        onCheckedChange={handleDeletionCheck}
        className="absolute text-blue outline-none bg-white left-2 top-2 z-50 w-5 h-5"
      />
      <div className="w-full h-[230px] relative flex items-center justify-center bg-gray-50">
        {catalog.image ? (
          <Image
            alt="Catalog image"
            src={catalogImage}
            width={280}
            height={210}
            className="group-hover:opacity-80 object-contain max-w-full max-h-full"
            unoptimized
            onError={() => setCatalogImage("/not-found/image.png")}
          />
        ) : (
          <div className="bg-gray w-full h-full group-hover:opacity-80 flex justify-center items-center">
            <BigXMark width={30} height={30} />
          </div>
        )}
        <div
          onClick={() => {
            props.onEdit();
          }}
          className="cursor-pointer group absolute group-hover:flex items-center hidden px-4 py-2 bg-white border-gray border rounded-s  space-x-2 active:scale-95 active:opacity-85 duration-300"
        >
          <EditionIcon />
          <Text textStyle="TS5" className="text-blue">
            {t("edit")}
          </Text>
        </div>
      </div>
      <div className="flex-1 flex flex-col justify-center px-3">
        <Text textStyle="TS5" className="font-bold text-black">
          {catalog.name}
        </Text>
        {catalog.description ? (
          <Text textStyle="TS6" className="text-gray">
            {catalog.description}
          </Text>
        ) : null}
      </div>
    </div>
  );
}
