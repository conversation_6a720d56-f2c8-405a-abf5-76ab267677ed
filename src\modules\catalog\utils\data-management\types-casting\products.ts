import {
  ItemInResponseType,
  ItemType,
  ProductInResponseType,
  ProductType,
} from "@/modules/catalog/types/products";
import { castToStoryType } from "./stories";
import { castToIngredientType } from "./ingredients";

export function castToProductType(
  productInResponse: ProductInResponseType
): ProductType {
  return {
    slug: productInResponse.slug,
    metaContent: productInResponse.metaContent
      ? {
          title: productInResponse.metaContent.title,
          description: productInResponse.metaContent.description,
          keywords: productInResponse.metaContent.tags,
        }
      : null,
    brand: productInResponse.brand,
    categoryIds: productInResponse.categoryIds,
    name: productInResponse.name as string,
    displayOrder: productInResponse.displayOrder,
    description: productInResponse.description as string,
    details: productInResponse.details as string,
    id: productInResponse.id,
    items: productInResponse.items.map((item) =>
      castToItemType(item, productInResponse)
    ),
    stories:
      productInResponse.stories?.map((story) => castToStoryType(story)) || [],
    ingredients:
      productInResponse.ingredients?.map((ingredient) =>
        castToIngredientType(ingredient)
      ) || [],
    sections: productInResponse.sections || [],
    faqs: productInResponse.faqs || [],
  };
}

function castToItemType(
  item: ItemInResponseType,
  product: ProductInResponseType
): ItemType {
  return {
    name: product.name,
    id: item.id,
    reference: item.reference,
    barcode: item.barcode,
    quantity: item.quantity,
    image: `${process.env.BACKEND_ADDRESS}${
      item.image ? item.image : `/${item.image}`
    }`,
    images: item.images.map(
      (image) => `${process.env.BACKEND_ADDRESS}${image ? image : `/${image}`}`
    ),
    variations: item.variation || [],
    online: item.online,
    prices: item.prices.map((price) => {
      return {
        promotionalPrice: Number(price.promotionalPrice),
        realPrice: Number(price.regularPrice),
        currency: price.currency,
      };
    }),
    inStock: item.inStock,
  };
}
