"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Edit, Trash2 } from "lucide-react";
import Text from "@/styles/text-styles";
import { StoryType } from "@/modules/catalog/types/products";

interface StoryCardProps {
  story: StoryType;
  index?: number;
  onEdit: (story: StoryType) => void;
  onDelete: (story: StoryType) => void;
  className?: string;
}

export default function StoryCard({
  story,
  index,
  onEdit,
  onDelete,
  className = "",
}: StoryCardProps) {
  return (
    <Card key={story.id || index} className={`p-4 ${className}`}>
      <div className="flex justify-between items-start">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-2">
            <Text textStyle="TS6" className="font-bold">
              {story.title}
            </Text>
          </div>
          <Text textStyle="TS7" className="text-gray-600 mb-2">
            {story.description}
          </Text>
          {story.image && (
            <div className="mt-2">
              <img
                src={story.image}
                alt={story.title}
                className="w-20 h-20 object-cover rounded"
              />
            </div>
          )}
        </div>
        <div className="flex gap-2 ml-4">
          <Button
            type="button"
            variant="outline"
            size="icon"
            onClick={() => onEdit(story)}
            className="text-blue-500 hover:text-blue-700"
          >
            <Edit className="w-4 h-4" />
          </Button>
          <Button
            type="button"
            variant="outline"
            size="icon"
            onClick={() => onDelete(story)}
            className="text-red-500 hover:text-red-700"
          >
            <Trash2 className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </Card>
  );
}
