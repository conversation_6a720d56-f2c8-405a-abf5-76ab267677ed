import { useQueryClient } from "@tanstack/react-query";
import { useState, useRef, FormEvent } from "react";
import { useTranslations } from "next-intl";
import { useToast } from "@/hooks/use-toast";
import { CustomError } from "@/utils/custom-error";
import { IngredientType } from "@/modules/catalog/types/products";

import uploadIngredientToServerSide from "../../services/ingredients/ingredient-upload";
import cleanIngredientFormData from "../../utils/form-data-cleaning/ingredient-form";
import { validateIngredientData } from "../../validation/ingredients/validate-ingredient-data";

interface UseIngredientUploadProps {
  productId: string;
  ingredient?: IngredientType | null;
  isEditing?: boolean;
  onSuccess?: () => void;
}

export default function useIngredientUpload({
  productId,
  ingredient,
  isEditing = false,
  onSuccess,
}: UseIngredientUploadProps) {
  const queryClient = useQueryClient();
  const t = useTranslations("warnings");
  const { toast } = useToast();
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingIngredient, setEditingIngredient] = useState<IngredientType | null>(null);
  const [warning, setWarning] = useState("");
  const [isPending, setIsPending] = useState(false);
  const formRef = useRef<HTMLFormElement>(null);

  const handleCreateIngredient = () => {
    setIsCreateDialogOpen(true);
  };

  const handleEditIngredient = (ingredient: IngredientType) => {
    setEditingIngredient(ingredient);
  };

  const handleCreateSuccess = () => {
    setIsCreateDialogOpen(false);
    onSuccess?.();
  };

  const handleEditSuccess = () => {
    setEditingIngredient(null);
    onSuccess?.();
  };

  const handleCloseCreateDialog = () => {
    setIsCreateDialogOpen(false);
  };

  const handleCloseEditDialog = () => {
    setEditingIngredient(null);
  };

  const submitIngredient = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!formRef.current) return;

    setIsPending(true);

    try {
      const formData = new FormData(formRef.current);
      const cleanedFormData = cleanIngredientFormData(
        formData,
        productId,
        isEditing && ingredient ? ingredient.id : undefined
      );

      validateIngredientData(cleanedFormData, isEditing);
      setWarning("");

      await uploadIngredientToServerSide(
        cleanedFormData,
        isEditing && ingredient ? ingredient.id : undefined
      );

      queryClient.invalidateQueries({
        queryKey: ["product", productId],
        exact: false,
      });

      toast({
        title: "Succès",
        description: isEditing
          ? "Ingrédient mis à jour avec succès"
          : "Ingrédient créé avec succès",
      });

      onSuccess?.();
    } catch (error) {
      const customError = error as CustomError;
      let errorMessage = t("serverError");

      if (customError.status === 400) {
        errorMessage = t("upload.missedData");
      } else if (customError.status === 404) {
        errorMessage = "Ingrédient ou produit non trouvé";
      }

      setWarning(errorMessage);
    } finally {
      setIsPending(false);
    }
  };

  return {
    isPending,
    submitIngredient,
    warning,
    formRef,
    isCreateDialogOpen,
    editingIngredient,
    handleCreateIngredient,
    handleEditIngredient,
    handleCreateSuccess,
    handleEditSuccess,
    handleCloseCreateDialog,
    handleCloseEditDialog,
  };
}
