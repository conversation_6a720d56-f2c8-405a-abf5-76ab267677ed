import { useQueryClient } from "@tanstack/react-query";
import { FormEvent, useRef, useState } from "react";
import { CustomError } from "@/utils/custom-error";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import { MultilanguageSeoContentPayload } from "@/modules/seo/types/multilanguage-seo";
import { usePreviousUrl } from "@/hooks/urls-management/use-previous-url";
import cleanBrandFormData from "../../utils/form-data-cleaning/brand-form";
import { validateBrandData } from "../../validation/brands/validate-brand-data";
import uploadBrandToServerSide from "../../services/brands/brand-upload";

export default function useBrandCreation(
  getMetaContent: () => MultilanguageSeoContentPayload
) {
  const t = useTranslations("warnings");
  const brandsT = useTranslations("BrandsManagement");

  const queryClient = useQueryClient();
  const router = useRouter();
  const previousUrl = usePreviousUrl();

  const formRef = useRef<HTMLFormElement>(null);
  const [warning, setWarning] = useState("");
  const [isPending, setIsPending] = useState(false);
  const [activeLanguage, setActiveLanguage] = useState("french");

  const { toast } = useToast();

  const handleLanguageChange = (language: string) => {
    if (language === "arabic") setActiveLanguage("arabic");
    else if (language === "french") setActiveLanguage("french");
    else if (language === "english") setActiveLanguage("english");
  };

  async function submitBrand(event: FormEvent) {
    if (!formRef.current) return;

    setIsPending(true);
    event.preventDefault();
    try {
      const formData = new FormData(formRef.current);

      const filteredFormData = cleanBrandFormData(formData);
      validateBrandData(filteredFormData);

      const metaContentData = getMetaContent();
      filteredFormData.append(
        "metaContent",
        JSON.stringify(metaContentData.content)
      );

      setWarning("");

      await uploadBrandToServerSide(filteredFormData);

      queryClient.invalidateQueries({
        queryKey: ["brands"],
        exact: false,
      });

      if (previousUrl && previousUrl.startsWith("/brands"))
        router.push(previousUrl);
      else router.push("/brands");
    } catch (error) {
      const customError = error as CustomError;
      if (customError.status === 500) {
        setWarning(t("serverError"));
        toast({ title: t("warning"), description: t("serverError") });
      } else if (customError.status === 400) {
        if (customError.message === "Missed Data!") {
          setWarning(t("upload.missedData"));
          toast({ title: t("warning"), description: t("upload.missedData") });
        } else {
          setWarning(t("upload.invalidFormat"));
          toast({
            title: t("warning"),
            description: t("upload.invalidFormat"),
          });
        }
      }
    } finally {
      setIsPending(false);
    }
  }

  return {
    submitBrand,
    formRef,
    warning,
    isPending,
    activeLanguage,
    handleLanguageChange,
  };
}
