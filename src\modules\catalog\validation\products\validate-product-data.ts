import { CustomError } from "@/utils/custom-error";
import { ProductUploadType } from "../../types/products";

export function validateProductData(data: ProductUploadType): void {
  const missingFields: string[] = [];

  if (!data.name || data.name.trim() === "") {
    missingFields.push("name");
  }

  if (!data.categoryIds || data.categoryIds.length === 0) {
    missingFields.push("categoryIds");
  }

  if (data.sections) {
    data.sections.forEach((section, index) => {
      if (!section.title || section.title.trim() === "") {
        missingFields.push(`sections[${index}].title`);
      }
      if (!section.description || section.description.trim() === "") {
        missingFields.push(`sections[${index}].description`);
      }
    });
  }

  if (data.faqs) {
    data.faqs.forEach((faq, index) => {
      if (!faq.question || faq.question.trim() === "") {
        missingFields.push(`faqs[${index}].question`);
      }
      if (!faq.answer || faq.answer.trim() === "") {
        missingFields.push(`faqs[${index}].answer`);
      }
    });
  }

  if (missingFields.length > 0) {
    throw new CustomError("Missed Data!", 400);
  }

  if (data.displayOrder !== undefined && data.displayOrder < 1) {
    throw new CustomError("Invalid Data", 400);
  }
}
