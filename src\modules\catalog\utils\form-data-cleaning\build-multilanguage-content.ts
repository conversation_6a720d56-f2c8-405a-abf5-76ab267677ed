interface ContentField {
  name?: string;
  description?: string;
  details?: string;
  language: string;
}

export function buildMultilanguageContent(
  formData: FormData,
  currentLanguage: string,
  includeDetails: boolean = false,
  isEdition: boolean = true
): ContentField[] {
  const content: ContentField[] = [];

  ["arabic", "french", "english"].forEach((lang) => {
    const nameField = formData.get(`name_${lang}`);
    const descField = formData.get(`description_${lang}`);
    const detailsField = includeDetails
      ? formData.get(`details_${lang}`)
      : null;

    if (lang === currentLanguage) {
      const contentItem: ContentField = {
        name: nameField?.toString() || "",
        description: descField?.toString() || "",
        language: lang.charAt(0).toUpperCase() + lang.slice(1),
      };

      if (includeDetails) {
        contentItem.details = detailsField?.toString() || "";
      }

      content.push(contentItem);
    } else {
      if (isEdition) {
        // For edition forms: unchanged languages only include the language property
        const contentItem: ContentField = {
          language: lang.charAt(0).toUpperCase() + lang.slice(1),
        };
        content.push(contentItem);
      } else {
        // For creation forms: include empty fields for validation
        const contentItem: ContentField = {
          name: "",
          description: "",
          language: lang.charAt(0).toUpperCase() + lang.slice(1),
        };

        if (includeDetails) {
          contentItem.details = "";
        }

        content.push(contentItem);
      }
    }
  });

  return content;
}
