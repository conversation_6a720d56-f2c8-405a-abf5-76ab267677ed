import { useQuery } from "@tanstack/react-query";
import useUser from "@/modules/auth/hooks/use-user";
import usePagination from "@/hooks/use-pagination";
import { retrieveReviews } from "../services/reviews-extraction";
import { ReviewType } from "../types/reviews";

interface PaginationType {
  records: number;
  currentPage: number;
  totalPages: number;
}

export default function useReviews(productSlug: string, limit: number = 5) {
  const { user, isLoading: userIsLoading } = useUser();
  const { page, setPage, pagesNumber, setPagesNumber, records, setRecords } =
    usePagination();

  const { data, isLoading, isError } = useQuery<{
    reviews: ReviewType[];
    pagination: PaginationType;
  } | null>({
    queryKey: ["reviews", productSlug, page, user],
    queryFn: () => retrieveReviews(productSlug, page, limit),
    enabled: !userIsLoading && user !== null && !!productSlug,
    placeholderData: (prev) => prev,
  });

  // Update pagination when data changes
  if (data?.pagination) {
    if (pagesNumber !== data.pagination.totalPages) {
      setPagesNumber(data.pagination.totalPages);
    }
    if (records !== data.pagination.records) {
      setRecords(data.pagination.records);
    }
  }

  return {
    reviews: data?.reviews || [],
    reviewsAreLoading: isLoading,
    reviewsError: isError,
    page,
    setPage,
    pagesNumber,
    records,
  };
}
