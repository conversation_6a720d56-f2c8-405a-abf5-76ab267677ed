import { buildMultilanguageContent } from "./build-multilanguage-content";

interface FaqArrays {
  updatedFAQs: any[];
  newFAQs: any[];
  deletedFAQIds: string[];
}

interface SectionArrays {
  updatedSections: any[];
  newSections: any[];
  deletedSectionIds: string[];
}

function separateFaqsIntoArrays(
  currentFaqs: any[],
  originalFaqs: any[]
): FaqArrays {
  const updatedFAQs: any[] = [];
  const newFAQs: any[] = [];
  const deletedFAQIds: string[] = [];

  const originalFaqIds = new Set(
    originalFaqs.map((faq) => faq.id).filter(Boolean)
  );
  const currentFaqIds = new Set(
    currentFaqs.map((faq) => faq.id).filter(Boolean)
  );

  currentFaqs.forEach((faq) => {
    if (faq.id && originalFaqIds.has(faq.id)) {
      updatedFAQs.push(faq);
    } else {
      newFAQs.push(faq);
    }
  });

  originalFaqs.forEach((originalFaq) => {
    if (originalFaq.id && !currentFaqIds.has(originalFaq.id)) {
      deletedFAQIds.push(originalFaq.id);
    }
  });

  return { updatedFAQs, newFAQs, deletedFAQIds };
}

function separateSectionsIntoArrays(
  currentSections: any[],
  originalSections: any[]
): SectionArrays {
  const updatedSections: any[] = [];
  const newSections: any[] = [];
  const deletedSectionIds: string[] = [];

  const originalSectionIds = new Set(
    originalSections.map((section) => section.id).filter(Boolean)
  );
  const currentSectionIds = new Set(
    currentSections.map((section) => section.id).filter(Boolean)
  );

  currentSections.forEach((section) => {
    if (section.id && originalSectionIds.has(section.id)) {
      updatedSections.push(section);
    } else {
      newSections.push(section);
    }
  });

  originalSections.forEach((originalSection) => {
    if (originalSection.id && !currentSectionIds.has(originalSection.id)) {
      deletedSectionIds.push(originalSection.id);
    }
  });

  return { updatedSections, newSections, deletedSectionIds };
}

export function cleanProductEditionFormData(
  formData: FormData,
  currentLanguage: string,
  originalFaqs: any[] = [],
  originalSections: any[] = []
): FormData {
  const cleanedData = new FormData();
  const categoryIds: string[] = [];

  const excludedFields = [
    "keywords",
    "seoContent",
    "metaTitle_arabic",
    "metaDescription_arabic",
    "metaTitle_french",
    "metaDescription_french",
    "metaTitle_english",
    "metaDescription_english",
  ];

  const sectionContentMap: Record<number, Record<string, any>> = {};
  const sections: Array<{ id?: string; displayOrder: number; content: any[] }> =
    [];

  const faqContentMap: Record<number, Record<string, any>> = {};
  const faqs: Array<{ id?: string; displayOrder: number; content: any[] }> = [];

  const content = buildMultilanguageContent(formData, currentLanguage, true);

  for (const [key, value] of formData.entries()) {
    const stringValue = value as string;

    if (excludedFields.includes(key)) continue;

    if (key === "categoryIds") {
      categoryIds.push(stringValue);
    } else if (key.startsWith("sections[")) {
      const sectionMatch = key.match(/sections\[(\d+)\]\.(.+)_(.+)/);
      const displayOrderMatch = key.match(/sections\[(\d+)\]\.displayOrder$/);
      const idMatch = key.match(/sections\[(\d+)\]\.id$/);

      if (sectionMatch) {
        const index = parseInt(sectionMatch[1]);
        const field = sectionMatch[2];
        const language = sectionMatch[3];

        sectionContentMap[index] ||= {};
        sectionContentMap[index][language] ||= {
          language: language.charAt(0).toUpperCase() + language.slice(1),
        };
        sectionContentMap[index][language][field] = stringValue;
      } else if (displayOrderMatch) {
        const index = parseInt(displayOrderMatch[1]);
        sections[index] ||= { displayOrder: index + 1, content: [] };
        sections[index].displayOrder = parseInt(stringValue) || index + 1;
      } else if (idMatch) {
        const index = parseInt(idMatch[1]);
        sections[index] ||= { displayOrder: index + 1, content: [] };
        sections[index].id = stringValue;
      }
    } else if (key.startsWith("faqs[")) {
      const faqMatch = key.match(/faqs\[(\d+)\]\.(.+)_(.+)/);
      const displayOrderMatch = key.match(/faqs\[(\d+)\]\.displayOrder$/);
      const idMatch = key.match(/faqs\[(\d+)\]\.id$/);

      if (faqMatch) {
        const index = parseInt(faqMatch[1]);
        const field = faqMatch[2];
        const language = faqMatch[3];

        faqContentMap[index] ||= {};
        faqContentMap[index][language] ||= {
          language: language.charAt(0).toUpperCase() + language.slice(1),
        };
        faqContentMap[index][language][field] = stringValue;
      } else if (displayOrderMatch) {
        const index = parseInt(displayOrderMatch[1]);
        faqs[index] ||= { displayOrder: index + 1, content: [] };
        faqs[index].displayOrder = parseInt(stringValue) || index + 1;
      } else if (idMatch) {
        const index = parseInt(idMatch[1]);
        faqs[index] ||= { displayOrder: index + 1, content: [] };
        faqs[index].id = stringValue;
      }
    } else if (
      !key.includes("_arabic") &&
      !key.includes("_french") &&
      !key.includes("_english")
    ) {
      if (key === "brandId" && stringValue === "") {
        cleanedData.append(key, "null");
      } else {
        cleanedData.append(key, value);
      }
    }
  }

  categoryIds.forEach((id) => cleanedData.append("categoryIds", id));

  if (content.length > 0) {
    cleanedData.append("content", JSON.stringify(content));
  }

  // Finalize sections
  Object.keys(sectionContentMap).forEach((indexStr) => {
    const index = parseInt(indexStr);
    sections[index] ||= { displayOrder: index + 1, content: [] };

    ["arabic", "french", "english"].forEach((lang) => {
      const languageKey = lang.charAt(0).toUpperCase() + lang.slice(1);
      const langContent = sectionContentMap[index]?.[lang] || {
        title: "",
        description: "",
        language: languageKey,
      };

      sections[index].content.push({
        title: langContent.title || "",
        description: langContent.description || "",
        language: languageKey,
      });
    });
  });

  // Finalize FAQs
  Object.keys(faqContentMap).forEach((indexStr) => {
    const index = parseInt(indexStr);
    faqs[index] ||= { displayOrder: index + 1, content: [] };

    ["arabic", "french", "english"].forEach((lang) => {
      const languageKey = lang.charAt(0).toUpperCase() + lang.slice(1);
      const langContent = faqContentMap[index]?.[lang] || {
        question: "",
        answer: "",
        language: languageKey,
      };

      faqs[index].content.push({
        question: langContent.question || "",
        answer: langContent.answer || "",
        language: languageKey,
      });
    });
  });

  // Separate sections into arrays
  const sectionArrays = separateSectionsIntoArrays(sections, originalSections);

  if (sectionArrays.updatedSections.length > 0) {
    cleanedData.append(
      "updatedSections",
      JSON.stringify(sectionArrays.updatedSections)
    );
  }

  if (sectionArrays.newSections.length > 0) {
    cleanedData.append(
      "newSections",
      JSON.stringify(sectionArrays.newSections)
    );
  }

  if (sectionArrays.deletedSectionIds.length > 0) {
    cleanedData.append(
      "deletedSectionIds",
      JSON.stringify(sectionArrays.deletedSectionIds)
    );
  }

  // Separate FAQs into arrays
  const faqArrays = separateFaqsIntoArrays(faqs, originalFaqs);

  if (faqArrays.updatedFAQs.length > 0) {
    cleanedData.append("updatedFAQs", JSON.stringify(faqArrays.updatedFAQs));
  }

  if (faqArrays.newFAQs.length > 0) {
    cleanedData.append("newFAQs", JSON.stringify(faqArrays.newFAQs));
  }

  if (faqArrays.deletedFAQIds.length > 0) {
    cleanedData.append(
      "deletedFAQIds",
      JSON.stringify(faqArrays.deletedFAQIds)
    );
  }

  return cleanedData;
}
