"use client";

import WarnInput from "@/components/input/warn-input";
import ImagesUpload from "@/modules/catalog/components/images-management/images-upload";
import { useEffect, useState } from "react";
import FormSubmission from "../../form-submission";
import { useTranslations } from "next-intl";
import { Label } from "@/components/ui/label";
import { ItemType } from "@/modules/catalog/types/products";
import ImageUpload from "../../images-management/image-upload";
import useItemUpload from "@/modules/catalog/hooks/items/use-item-upload";
import { useRouter, useSearchParams } from "next/navigation";
import useEditableProduct from "@/modules/catalog/hooks/products/use-editable-product";
import { Card } from "@/components/ui/card";
import Text from "@/styles/text-styles";
import { Switch } from "@/components/ui/switch";
import { usePreviousUrl } from "@/hooks/urls-management/use-previous-url";
import { getPreviousUrlQueryParam } from "@/utils/previous-url-params";
import { disableScrollOnNumberInput } from "@/utils/number-input";

interface Props {
  productSlug: string;
}

export default function ItemEdition({ productSlug }: Props) {
  const uploadContent = useTranslations("shared.forms.upload");
  const t = useTranslations("ItemsManagement");

  const searchParams = useSearchParams();
  const previousUrl = usePreviousUrl();
  const router = useRouter();

  const itemId = searchParams.get("itemId");

  const [item, setItem] = useState<ItemType>();
  const [isOnline, setIsOnline] = useState<boolean | undefined>(undefined);
  const [itemImages, setItemImages] = useState<string[]>([]);

  const { product, isLoading: productIsLoading } = useEditableProduct(
    productSlug || ""
  );

  useEffect(() => {
    if (product && itemId) {
      const foundItem = product.items.find((item) => item.id === itemId);
      setItem(foundItem);
      setIsOnline(foundItem?.online);
      if (foundItem) setItemImages(foundItem.images);
    }
  }, [product, itemId]);

  const onFinish = () => {
    if (previousUrl)
      router.replace(
        `/products/${productSlug}/edition&${getPreviousUrlQueryParam(
          previousUrl
        )}`
      );
    else router.replace(`/products/${productSlug}/edition`);
  };

  const {
    formRef,
    submitItem,
    warning,
    isPending: uploadingIsPending,
    setNewImages,
    setDeletedImages,
  } = useItemUpload({
    itemId: item?.id,
    productSlug: product ? product.slug : "",
  });

  return (
    item && (
      <FormSubmission
        cancel={uploadContent("cancel")}
        submit={uploadContent("save")}
        onCancel={onFinish}
        onSubmit={submitItem}
        isPending={uploadingIsPending}
        hideTopButtons
      >
        <form ref={formRef} className="flex flex-col space-y-3">
          <Card className="flex flex-col gap-4 px-8 py-6">
            <Text textStyle="TS5" className="font-bold text-black">
              {t("itemInfo")}
            </Text>
            <div className="text-red">{warning}</div>

            <div className="flex flex-col space-y-6">
              {/* Barcode */}
              <div className="w-full flex flex-col space-y-1">
                <Label htmlFor="barcode">
                  {uploadContent("itemLabels.barcode")}{" "}
                  {uploadContent("required")}
                </Label>
                <WarnInput
                  id="barcode"
                  type="text"
                  name="barcode"
                  warning=""
                  value={item.barcode || ""}
                />
              </div>

              {/* Availability Toggle */}
              <Label htmlFor="online">
                {uploadContent("itemLabels.online")}
              </Label>
              <div className="w-full flex gap-3 ">
                <Text textStyle="TS6" className="text-gray">
                  {uploadContent("itemLabels.true")}
                </Text>
                <Switch
                  id="online"
                  checked={isOnline}
                  onCheckedChange={() => setIsOnline((prev) => !prev)}
                />
                {/* Hidden input to capture switch state for form submission */}
                <input
                  type="hidden"
                  name="online"
                  value={isOnline ? "true" : "false"}
                />
              </div>

              <div className="flex flex-col 2L:flex-row gap-7">
                {/* Quantity */}
                <div className="flex-1 flex-col space-y-1">
                  <Label htmlFor="quantity">
                    {uploadContent("itemLabels.quantity")}
                    {uploadContent("required")}
                  </Label>
                  <WarnInput
                    id="quantity"
                    type="number"
                    min="1"
                    onWheel={disableScrollOnNumberInput}
                    name="quantity"
                    warning=""
                    value={item.quantity === 0 ? "1" : item.quantity || "1"}
                  />
                </div>

                {/* Prices */}
                <div className="flex-1 flex-col space-y-1">
                  <Label htmlFor="price">
                    {uploadContent("itemLabels.regularPrice")}
                    {uploadContent("required")}
                  </Label>
                  <WarnInput
                    id="price"
                    type="number"
                    min="0.01"
                    step="0.01"
                    onWheel={disableScrollOnNumberInput}
                    name="price"
                    warning=""
                    value={
                      item.prices[0].realPrice === 0
                        ? "0.01"
                        : item.prices[0].realPrice || "0.01"
                    }
                  />
                </div>
              </div>

              <hr className="border-t border-light-gray my-4" />

              <Text textStyle="TS6" className="font-bold text-black">
                {t("itemSpecialImages")}
              </Text>

              {/* Image Upload */}
              <div className="w-full flex flex-col space-y-1">
                <label className="text-gray">
                  {uploadContent("itemLabels.image")}{" "}
                </label>
                <ImageUpload defaultSrc={item.image} name="image" />
              </div>

              {/* Images Upload */}
              <div className="w-full flex flex-col space-y-1">
                <label className="text-gray">
                  {uploadContent("itemLabels.images")}
                </label>
                <ImagesUpload
                  name="images"
                  setDefaultPreviews={setItemImages}
                  defaultPreviews={itemImages}
                  setNewImages={setNewImages}
                  setDeletedImages={setDeletedImages}
                  mode="edition"
                />
              </div>

              {/* Variation Name & Value */}
              {/* {item.variations.map((variation, idx) => (
              <div key={idx} className="w-full flex flex-col space-y-1">
                <Label htmlFor="variationName">
                  {uploadContent("itemLabels.variationName")}{" "}
                  {uploadContent("optional")}
                </Label>
                <WarnInput
                  id="variationName"
                  type="text"
                  name="variationName"
                  warning=""
                  value={item.variations[0].value}
                />
              </div>
            ))} */}
            </div>
          </Card>
        </form>
      </FormSubmission>
    )
  );
}
