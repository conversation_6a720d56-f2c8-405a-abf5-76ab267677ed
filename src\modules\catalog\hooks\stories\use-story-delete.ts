import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import { useTranslations } from "next-intl";
import { useToast } from "@/hooks/use-toast";
import { CustomError } from "@/utils/custom-error";
import { StoryType } from "@/modules/catalog/types/products";
import deleteStoryOnServerSide from "../../services/stories/story-delete";

interface UseStoryDeleteProps {
  productId: string;
  onSuccess?: () => void;
}

export default function useStoryDelete({
  productId,
  onSuccess,
}: UseStoryDeleteProps) {
  const queryClient = useQueryClient();
  const t = useTranslations("warnings");
  const { toast } = useToast();
  const [deletingStory, setDeletingStory] = useState<StoryType | null>(null);

  const { mutate: deleteStory, isPending } = useMutation<
    void,
    CustomError,
    StoryType
  >({
    mutationFn: (story: StoryType) => deleteStoryOnServerSide(story),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["product", productId],
        exact: false,
      });

      toast({
        title: "Succès",
        description: "Histoire supprimée avec succès",
      });

      setDeletingStory(null);
      onSuccess?.();
    },
    onError: (error) => {
      let errorMessage = t("serverError");

      if (error.status === 400) {
        errorMessage = t("upload.missedData");
      } else if (error.status === 404) {
        errorMessage = "Histoire ou produit non trouvé";
      }

      toast({
        title: "Erreur",
        description: errorMessage,
        variant: "destructive",
      });
    },
  });

  const handleDeleteStory = (story: StoryType) => {
    setDeletingStory(story);
  };

  const handleConfirmDelete = () => {
    if (deletingStory) {
      deleteStory(deletingStory);
    }
  };

  const handleCancelDelete = () => {
    setDeletingStory(null);
  };

  return {
    deleteStory,
    isPending,
    deletingStory,
    handleDeleteStory,
    handleConfirmDelete,
    handleCancelDelete,
  };
}
