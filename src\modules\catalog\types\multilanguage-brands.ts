import {
  SeoMetaContentType,
  SeoMetaContentTypeInResponse,
} from "@/modules/seo/types";

export interface MultilanguageBrandContent {
  name: string;
  description: string;
  language: string;
}

export interface MultilanguageBrandType {
  id: string;
  content: MultilanguageBrandContent[];
  image: string;
  displayOrder: number;
  metaContent: SeoMetaContentType | null;
  slug: string;
}

export interface MultilanguageBrandInResponseType {
  id: string;
  content: MultilanguageBrandContent[];
  image: string;
  displayOrder: number;
  metaContent: SeoMetaContentTypeInResponse;
  slug: string;
}

export type LanguageKey = "arabic" | "french" | "english";
