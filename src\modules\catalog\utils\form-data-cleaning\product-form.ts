import { ProductUploadType } from "../../types/products";

export default function cleanProductFormData(
  formData: FormData,
  elementId?: string
): ProductUploadType {
  const data: any = {};
  const categoryIds: string[] = [];
  const sections: any[] = [];
  const faqs: any[] = [];
  const ingredients: any[] = [];
  const stories: any[] = [];

  formData.forEach((value, key) => {
    const stringValue = value as string;

    if (key === "categoryIds") {
      if (stringValue.trim() !== "") {
        categoryIds.push(stringValue);
      }
    } else if (key.startsWith("sections[")) {
      const match = key.match(/sections\[(\d+)\]\.(.+)/);
      if (match) {
        const index = parseInt(match[1]);
        const field = match[2];
        if (!sections[index]) {
          sections[index] = { displayOrder: index + 1 };
        }
        sections[index][field] =
          field === "displayOrder"
            ? parseInt(stringValue) || index + 1
            : stringValue;
      }
    } else if (key.startsWith("faqs[")) {
      const match = key.match(/faqs\[(\d+)\]\.(.+)/);
      if (match) {
        const index = parseInt(match[1]);
        const field = match[2];
        if (!faqs[index]) {
          faqs[index] = { displayOrder: index + 1 };
        }
        faqs[index][field] =
          field === "displayOrder"
            ? parseInt(stringValue) || index + 1
            : stringValue;
      }
    } else if (key === "displayOrder") {
      if (stringValue.trim() !== "") {
        data[key] = parseInt(stringValue);
      }
    } else if (key === "brandId") {
      if (!elementId && stringValue.trim() !== "") {
        data[key] = stringValue;
      } else if (elementId) {
        data[key] = stringValue === "" ? null : stringValue;
      }
    } else {
      if (!elementId && stringValue.trim() !== "") {
        data[key] = stringValue;
      } else if (elementId) {
        data[key] = stringValue;
      }
    }
  });

  data.categoryIds = categoryIds.filter((id) => id.trim() !== "");

  if (sections.length > 0) {
    data.sections = sections.filter(
      (section) => section && section.title && section.description
    );
  }

  if (faqs.length > 0) {
    data.faqs = faqs.filter((faq) => faq && faq.question && faq.answer);
  }

  if (ingredients.length > 0) {
    data.ingredients = ingredients.filter(
      (ingredient) => ingredient && ingredient.title && ingredient.description
    );
  }

  if (stories.length > 0) {
    data.stories = stories.filter(
      (story) => story && story.title && story.description
    );
  }

  return data as ProductUploadType;
}
