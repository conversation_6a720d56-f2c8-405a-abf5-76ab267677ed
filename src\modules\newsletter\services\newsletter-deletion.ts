import { DELETE } from "@/lib/http-methods";
import extractJWTokens from "@/modules/auth/utils/jwt/extract-tokens";
import { refreshToken } from "@/modules/auth/services/refresh-token";
import { AxiosError } from "axios";
import { CustomError } from "@/utils/custom-error";

type Response = {
  status: number;
  ok: boolean;
  error: string;
};

interface Params {
  emailId: string;
}

export async function deleteNewsletterEmailOnServerSide({
  emailId,
}: Params): Promise<Response> {
  const { access } = extractJWTokens();
  const headers = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const endpoint = `/newsletter/emails/${emailId}`;

    await DELETE(`${process.env.BACKEND_ADDRESS}${endpoint}`, headers);

    return { ok: true, status: 200, error: "" };
  } catch (error) {
    const axiosError = error as AxiosError;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() =>
        deleteNewsletterEmailOnServerSide({ emailId })
      );

      if (!res) throw new CustomError("Unauthorized!", 401);

      return res;
    } else if (axiosError.response?.status === 404) {
      const errorData = axiosError.response.data as {
        message: string;
        code: string;
      };

      if (errorData.code === "P6501") {
        throw new CustomError("This email is not found!", 404, "P6501");
      } else {
        throw new CustomError("Not Found", 404);
      }
    }

    throw new CustomError("Server Error!", 500);
  }
}
