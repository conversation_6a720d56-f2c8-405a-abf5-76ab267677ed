"use client";
import { AlertDialog } from "@/components/ui/alert-dialog";
import ModalDialog from "@/components/modal-dialog";
import { useTranslations } from "next-intl";
import { Skeleton } from "@/components/ui/skeleton";
import NoDataFound from "@/components/no-data-found";
import useReviews from "../hooks/use-reviews";
import useReviewDeletion from "../hooks/use-review-deletion";
import ReviewContainer from "./review-container";
import PaginationMangement from "@/components/pagination/pagination-management";
import DashboardListsContainer, {
  DashboardListsContainerSkeleton,
} from "@/components/dashbord-lists-container";

interface Props {
  productSlug: string;
}

export default function ReviewsList({ productSlug }: Props) {
  const t = useTranslations("ReviewsManagement");
  const { reviews, reviewsAreLoading, page, setPage, pagesNumber, records } =
    useReviews(productSlug, 5);

  const {
    isPending,
    alertModalIsOpen,
    warning,
    requestReviewDeletion,
    cancelDeletion,
    deleteReview,
  } = useReviewDeletion();

  return !reviewsAreLoading ? (
    <DashboardListsContainer
      title={t("title")}
      className="flex flex-col gap-4 pt-6"
    >
      <div className="flex flex-col gap-4">
        {reviews && reviews.length > 0 ? (
          <>
            <div className="space-y-3">
              {reviews.map((review) => (
                <ReviewContainer
                  key={review.id}
                  review={review}
                  onDelete={requestReviewDeletion}
                />
              ))}
            </div>

            {pagesNumber > 1 && (
              <div className="pt-3">
                <PaginationMangement
                  records={records}
                  pagesNumber={pagesNumber}
                  currentPage={page}
                  changePage={setPage}
                />
              </div>
            )}
          </>
        ) : (
          <NoDataFound className="py-10 w-full min-h-48 flex justify-center items-center" />
        )}

        <AlertDialog open={alertModalIsOpen}>
          <ModalDialog
            title={t("dialog.title")}
            details={t("dialog.details")}
            cancel={t("dialog.cancel")}
            confirm={t("dialog.confirm")}
            isPending={isPending}
            onCancel={cancelDeletion}
            onConfirm={deleteReview}
            warning={warning}
            theme="red"
          />
        </AlertDialog>
      </div>
    </DashboardListsContainer>
  ) : (
    <DashboardListsContainerSkeleton>
      <div className="flex-1 flex flex-col gap-3 pt-6">
        {Array.from({ length: 5 }).map((_, idx) => (
          <Skeleton key={idx} className="h-32 w-full rounded-lg" />
        ))}
      </div>
    </DashboardListsContainerSkeleton>
  );
}
