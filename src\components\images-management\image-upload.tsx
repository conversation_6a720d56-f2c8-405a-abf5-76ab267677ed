"use client";
import { useEffect, useRef, useState } from "react";
import { useTranslations } from "next-intl";
import Text from "@/styles/text-styles";
import Image from "next/image";

interface Props {
  defaultSrc?: string;
  name: string;
}

export default function ImageUpload({ defaultSrc, name }: Props) {
  const imageInputRef = useRef<HTMLInputElement>(null);
  const t = useTranslations("shared.forms.upload.file");
  const [preview, setPreview] = useState<string | null>(
    defaultSrc ? defaultSrc : null
  );
  const [fileInputKey, setFileInputKey] = useState(1);
  const [defaultImageIsDeleted, setDefaultImageIsDeleted] = useState(false);

  const [warning, setWarning] = useState("");

  useEffect(() => {
    if (defaultSrc) setPreview(defaultSrc);
  }, [defaultSrc]);

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      if (file.type.startsWith("image")) {
        if (file.size <= 4 * 1024 * 1024) {
          if (preview) {
            URL.revokeObjectURL(preview);
          }
          const newPreviewUrl = URL.createObjectURL(file);
          setPreview(newPreviewUrl);
          setWarning("");
        } else {
          setWarning(t("warnings.imageSize"));
          e.target.value = "";
        }
      } else {
        setWarning(t("warnings.fileType"));
        e.target.value = "";
      }
    }
  };

  const removeImage = () => {
    if (imageInputRef.current) {
      if (defaultSrc && defaultSrc !== preview) {
        imageInputRef.current.value = "";
        setPreview(defaultSrc);
        setDefaultImageIsDeleted(false);
      } else {
        setPreview(null);
        setDefaultImageIsDeleted(true);
      }
    }
    setFileInputKey(fileInputKey + 1);
  };

  return (
    <div className="w-full">
      {defaultImageIsDeleted && (
        <input type="hidden" name="deleteDefaultImage" value="true" />
      )}
      <div className="text-red text-sm">{warning}</div>
      <label
        className={`w-full rounded-sm border-dashed border-2 min-h-40 flex items-center justify-center mt-5 cursor-pointer ${
          preview && "hidden"
        }`}
      >
        <div className="flex flex-col items-center space-y-2">
          <Text
            textStyle="TS7"
            className="py-2 px-3 border rounded-lg h-fit w-fit text-blue"
          >
            {t("button")}
          </Text>
          <Text textStyle="TS7" className="text-gray">
            {t("description")}
          </Text>
          <Text textStyle="TS8" className="text-gray">
            {t("photoFormat")}
          </Text>
        </div>
        <input
          key={fileInputKey}
          name={name}
          ref={imageInputRef}
          type="file"
          accept="image/*"
          onChange={handleImageChange}
          className="m-[10px] hidden"
        />
      </label>
      {preview && (
        <div>
          <div className="relative group w-[200px] h-[200px] mt-4">
            <Image
              src={preview}
              unoptimized
              alt="Preview"
              fill
              className="rounded shadow object-cover"
            />
            <input
              key={fileInputKey}
              name={name}
              ref={imageInputRef}
              type="file"
              accept="image/*"
              onChange={handleImageChange}
              className="absolute inset-0 opacity-0 cursor-pointer"
            />
            <button
              type="button"
              onClick={removeImage}
              className="absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm opacity-0 group-hover:opacity-100 transition-opacity"
            >
              ×
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
