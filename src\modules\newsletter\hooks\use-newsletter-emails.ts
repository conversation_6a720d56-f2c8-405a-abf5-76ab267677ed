import { keepPreviousData, useQuery } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import { PaginationType } from "@/types/pagination";
import usePagination from "@/hooks/use-pagination";
import useUrlParams from "@/hooks/urls-management/use-url-params";
import { NewsletterEmailType } from "../types/newsletter";
import { retrieveNewsletterEmailsFromServerSide } from "../services/newsletter-extraction";

interface Params {
  paginationAffectUrl?: boolean;
  limit?: number;
}

export default function useNewsletterEmails({
  limit,
  paginationAffectUrl = false,
}: Params) {
  const [searchedEmails, setSearchedEmails] = useState("");
  const [searchVersion, setSearchVersion] = useState(0);

  const { page, setPage, pagesNumber, setPagesNumber, records, setRecords } =
    usePagination({ paginationAffectUrl });
  const { updateUrlParams, getParamFromUrl, removeParamFromUrl } =
    useUrlParams();

  const { data, isLoading, isError } = useQuery<{
    data: NewsletterEmailType[];
    pagination: PaginationType;
  } | null>({
    queryKey: ["newsletter-emails", page, searchedEmails],
    queryFn: () =>
      retrieveNewsletterEmailsFromServerSide({
        page,
        limit,
        searchedEmails,
      }),
    placeholderData: keepPreviousData,
  });

  useEffect(() => {
    if (
      data &&
      data.pagination &&
      pagesNumber !== data.pagination?.totalPages
    ) {
      setPagesNumber(data.pagination.totalPages);
      setRecords(data.data.length);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  // Get the searched emails from the URL
  useEffect(() => {
    if (typeof window !== "undefined") {
      const searchedEmailsInUrl = getParamFromUrl("search");

      if (searchedEmailsInUrl) setSearchedEmails(searchedEmailsInUrl);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Update the URL when the searched emails change
  useEffect(() => {
    if (searchedEmails !== "") {
      setPage(1);

      updateUrlParams("search", searchedEmails);
    } else {
      const searchInUrl = getParamFromUrl("search");

      if (searchInUrl) removeParamFromUrl("search");
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchVersion]);

  const handleSearch = (text: string) => {
    setSearchedEmails(text);
    setSearchVersion(searchVersion + 1);
  };

  return {
    emails: data && data.data ? data?.data : [],
    pagination: data?.pagination,
    isLoading,
    emailsAreLoading: isLoading,
    emailsError: isError,
    setPage,
    page,
    records,
    pagesNumber,
    searchedEmails,
    handleSearch,
  };
}
