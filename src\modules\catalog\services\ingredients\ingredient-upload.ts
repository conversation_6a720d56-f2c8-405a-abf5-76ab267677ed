import { POST, PATCH } from "@/lib/http-methods";
import extractJWTokens from "@/modules/auth/utils/jwt/extract-tokens";
import { AxiosError } from "axios";
import { refreshToken } from "@/modules/auth/services/refresh-token";
import { CustomError } from "@/utils/custom-error";
import {
  IngredientRegistrationResponse,
  ErrorDataResponse,
} from "../../types/products";

export default async function uploadIngredientToServerSide(
  formData: FormData,
  ingredientId?: string
): Promise<IngredientRegistrationResponse> {
  const { access } = extractJWTokens();
  const header = {
    Authorization: `Bearer ${access}`,
  };

  try {
    if (!ingredientId) {
      // Ingredient creation
      const endpoint = "/products/ingredients/register";

      const res = await POST(
        `${process.env.BACKEND_ADDRESS}${endpoint}`,
        header,
        formData
      );

      return res.data as IngredientRegistrationResponse;
    } else {
      // Ingredient edition
      const endpoint = `/products/ingredients/${ingredientId}`;

      const res = await PATCH(
        `${process.env.BACKEND_ADDRESS}${endpoint}`,
        header,
        formData
      );

      return res.data as IngredientRegistrationResponse;
    }
  } catch (error) {
    const axiosError = error as AxiosError<ErrorDataResponse>;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() =>
        uploadIngredientToServerSide(formData, ingredientId)
      );

      if (!res) throw new CustomError("Unauthorized", 401);
      return res;
    }

    if (axiosError.response?.status === 400) {
      throw new CustomError("Invalid Data", 400);
    }

    if (axiosError.response?.status === 404) {
      throw new CustomError("Ingredient or product not found", 404);
    }

    throw new CustomError("Server Error!", 500);
  }
}
