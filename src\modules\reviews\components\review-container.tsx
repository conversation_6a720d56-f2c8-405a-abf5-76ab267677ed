import { HTMLAttributes } from "react";
import { cn } from "@/lib/utils";
import Text from "@/styles/text-styles";
import { Button } from "@/components/ui/button";
import { Trash2 } from "lucide-react";
import { ReviewType } from "../types/reviews";
import { StarRating } from "@/components/ui/star-rating";

interface Props extends HTMLAttributes<"div"> {
  review: ReviewType;
  onDelete: (reviewId: string) => void;
}

export default function ReviewContainer({ review, ...props }: Props) {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("fr-FR", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  return (
    <div
      className={cn(
        "bg-white border rounded-lg p-4 hover:bg-gray-50 transition-colors",
        props.className
      )}
    >
      <div className="flex items-start justify-between gap-4">
        <div className="flex-1 space-y-3">
          {/* Header with customer info and rating */}
          <div className="flex items-center justify-between">
            <div className="flex flex-col ">
              <Text textStyle="TS5" className="text-blue font-bold">
                {review.user.name}
              </Text>
              <div>
                <Text textStyle="TS7">{review.user.email}</Text>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <StarRating rating={review.rating} size={16} />
              <Text textStyle="TS7" className="text-gray-600">
                {review.rating}/5
              </Text>
            </div>
          </div>

          {/* Comment */}
          <div className="bg-gray-50 rounded-lg p-3">
            <Text textStyle="TS7" className="text-gray-800 leading-relaxed">
              {review.comment}
            </Text>
          </div>

          {/* Date */}
          <div className="flex items-center justify-between">
            <Text textStyle="TS8" className="text-gray-500">
              {formatDate(review.createdAt)}
            </Text>
          </div>
        </div>

        {/* Delete button */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => props.onDelete(review.id)}
          className="text-red-600 hover:text-red-700 hover:bg-red-50 p-2"
        >
          <Trash2 className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
}
