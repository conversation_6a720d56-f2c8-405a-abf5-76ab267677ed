interface FaqContent {
  question: string;
  answer: string;
  language: string;
}

interface SectionContent {
  title: string;
  description: string;
  language: string;
}

interface MultilanguageFaq {
  displayOrder: number;
  content: FaqContent[];
}

interface MultilanguageSection {
  displayOrder: number;
  content: SectionContent[];
}

export function buildMultilanguageFaqs(formData: FormData): MultilanguageFaq[] {
  const faqs: MultilanguageFaq[] = [];
  const faqMap: { [key: number]: { [key: string]: any } } = {};

  // Process FAQ form data
  formData.forEach((value, key) => {
    const faqMatch = key.match(/faqs\[(\d+)\]\.(.+)_(.+)/);
    const displayOrderMatch = key.match(/faqs\[(\d+)\]\.displayOrder$/);

    if (faqMatch) {
      const index = parseInt(faqMatch[1]);
      const field = faqMatch[2];
      const language = faqMatch[3];

      if (!faqMap[index]) {
        faqMap[index] = {};
      }
      if (!faqMap[index][language]) {
        faqMap[index][language] = {
          language: language.charAt(0).toUpperCase() + language.slice(1),
        };
      }
      faqMap[index][language][field] = value as string;
    } else if (displayOrderMatch) {
      const index = parseInt(displayOrderMatch[1]);
      if (!faqs[index]) {
        faqs[index] = { displayOrder: index + 1, content: [] };
      }
      faqs[index].displayOrder = parseInt(value as string) || index + 1;
    }
  });

  // Build FAQ content for each FAQ
  Object.keys(faqMap).forEach((indexStr) => {
    const index = parseInt(indexStr);
    if (!faqs[index]) {
      faqs[index] = { displayOrder: index + 1, content: [] };
    }

    // Ensure all languages are included
    ["arabic", "french", "english"].forEach((lang) => {
      const languageKey = lang.charAt(0).toUpperCase() + lang.slice(1);
      const langContent = faqMap[index]?.[lang] || {
        question: "",
        answer: "",
        language: languageKey,
      };

      faqs[index].content.push({
        question: langContent.question || "",
        answer: langContent.answer || "",
        language: languageKey,
      });
    });
  });

  return faqs.filter(faq => faq !== undefined);
}

export function buildMultilanguageSections(formData: FormData): MultilanguageSection[] {
  const sections: MultilanguageSection[] = [];
  const sectionMap: { [key: number]: { [key: string]: any } } = {};

  // Process Section form data
  formData.forEach((value, key) => {
    const sectionMatch = key.match(/sections\[(\d+)\]\.(.+)_(.+)/);
    const displayOrderMatch = key.match(/sections\[(\d+)\]\.displayOrder$/);

    if (sectionMatch) {
      const index = parseInt(sectionMatch[1]);
      const field = sectionMatch[2];
      const language = sectionMatch[3];

      if (!sectionMap[index]) {
        sectionMap[index] = {};
      }
      if (!sectionMap[index][language]) {
        sectionMap[index][language] = {
          language: language.charAt(0).toUpperCase() + language.slice(1),
        };
      }
      sectionMap[index][language][field] = value as string;
    } else if (displayOrderMatch) {
      const index = parseInt(displayOrderMatch[1]);
      if (!sections[index]) {
        sections[index] = { displayOrder: index + 1, content: [] };
      }
      sections[index].displayOrder = parseInt(value as string) || index + 1;
    }
  });

  // Build Section content for each section
  Object.keys(sectionMap).forEach((indexStr) => {
    const index = parseInt(indexStr);
    if (!sections[index]) {
      sections[index] = { displayOrder: index + 1, content: [] };
    }

    // Ensure all languages are included
    ["arabic", "french", "english"].forEach((lang) => {
      const languageKey = lang.charAt(0).toUpperCase() + lang.slice(1);
      const langContent = sectionMap[index]?.[lang] || {
        title: "",
        description: "",
        language: languageKey,
      };

      sections[index].content.push({
        title: langContent.title || "",
        description: langContent.description || "",
        language: languageKey,
      });
    });
  });

  return sections.filter(section => section !== undefined);
}
